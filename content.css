#vm-query-button {
    position: fixed;
    right: -20px; /* 增加隐藏部分 */
    top: 50%;
    transform: translateY(-50%);
    z-index: 2147483647;
    background: linear-gradient(135deg, #ff8a00, #ff4646);
    color: white;
    border: none;
    border-radius: 6px 0 0 6px; /* 稍微增加圆角 */
    padding: 12px; /* 增加内边距 */
    cursor: pointer;
    width: 45px; /* 增加按钮大小 */
    height: 45px; /* 增加按钮大小 */
    box-shadow: -2px 2px 8px rgba(255, 70, 70, 0.3);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: move; /* 添加移动光标提示 */
    user-select: none; /* 防止文本选择 */
    touch-action: none; /* 优化触摸设备上的体验 */
    will-change: transform; /* 优化性能 */
}

#vm-query-button img {
    width: 25px; /* 增加图标大小 */
    height: 25px; /* 增加图标大小 */
}

#vm-query-button:hover {
    right: 0;
    background: linear-gradient(135deg, #ff9a20, #ff5656);
    box-shadow: -4px 2px 12px rgba(255, 70, 70, 0.4);
}

.floating-menu {
    position: fixed;
    right: 55px; /* 增加间距 */
    top: 50%;
    transform: translateY(-50%);
    background: white;
    border-radius: 6px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    display: none;
    z-index: 2147483647;
    overflow: hidden;
    min-width: 180px; /* 设置最小宽度 */
}

.floating-menu.show {
    display: block;
}

.floating-menu-item {
    position: relative;
    padding: 12px 20px; /* 增加内边距 */
    cursor: pointer;
    display: flex;
    align-items: center;
    transition: background-color 0.3s;
    font-size: 14px; /* 增加字体大小 */
}

.floating-menu-item:hover {
    background-color: #f0f0f0;
}

.submenu-arrow {
    margin-left: auto;
    font-size: 10px;
    color: #666;
}

.submenu {
    position: absolute;
    left: 100%;
    top: 0;
    background: white;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-radius: 4px;
    display: none;
    min-width: 180px; /* 增加子菜单宽度 */
    z-index: 2147483647; /* 与主菜单同级 */
}

.floating-menu-item:hover .submenu {
    display: block;
}

.submenu-item {
    padding: 8px 15px;
    cursor: pointer;
    display: flex;
    align-items: center;
    transition: background-color 0.3s;
}

.submenu-item:hover {
    background-color: #f0f0f0;
}

.floating-menu-item i,
.submenu-item i {
    margin-right: 8px;
    font-style: normal;
}

/* 保持其他样式不变 */
#vm-query-modal {
    position: fixed;
    right: 80px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 2147483645; /* 降低模态框层级 */
    background: white;
    border-radius: 8px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.2);
    padding: 0;
    margin: 0;
}

#vm-query-modal iframe {
    border: none;
    width: 1200px; /* 从1000px增加到1200px，增加20% */
    height: 1100px; /* 从600px增加到900px，增加50% */
    border-radius: 8px;
    display: block;
}

.modal-close {
    position: absolute;
    right: 10px;
    top: 10px;
    width: 20px;
    height: 20px;
    line-height: 20px;
    text-align: center;
    cursor: pointer;
    font-size: 20px;
    color: #666;
    z-index: 2147483646; /* 关闭按钮比模态框高一级 */
    background: white;
    border-radius: 50%;
}

.modal-close:hover {
    color: #333;
}

/* 添加拖动时的样式 */
.dragging {
    opacity: 0.8;
    cursor: grabbing !important;
    transition: none !important;
}

#vm-query-button:active {
    cursor: grabbing;
}
