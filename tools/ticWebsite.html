<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>TIC网站查询</title>
  <style>
    body {
      margin: 0;
      padding: 15px;
      font-family: Arial, sans-serif;
      font-size: 14px;
      background-color: #fff;
    }

    h2 {
      margin: 0 0 15px 0;
      font-size: 16px;
      color: #333;
    }

    .group-tabs {
      display: flex;
      gap: 10px;
      margin-bottom: 20px;
      flex-wrap: wrap;
    }

    .group-tab {
      padding: 8px 16px;
      border: 2px solid #ddd;
      border-radius: 6px;
      cursor: pointer;
      font-weight: bold;
      transition: all 0.3s ease;
      background-color: #f8f9fa;
      color: #333;
    }

    .group-tab:hover {
      transform: translateY(-2px);
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .group-tab.active {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }

    .group-tab.user-pc {
      border-color: #007bff;
      background-color: #e3f2fd;
    }

    .group-tab.user-pc.active {
      background-color: #007bff;
      color: white;
    }

    .group-tab.cs-pc {
      border-color: #28a745;
      background-color: #e8f5e8;
    }

    .group-tab.cs-pc.active {
      background-color: #28a745;
      color: white;
    }

    .group-tab.user-mobile {
      border-color: #ffc107;
      background-color: #fff8e1;
    }

    .group-tab.user-mobile.active {
      background-color: #ffc107;
      color: #212529;
    }

    .group-tab.tool {
      border-color: #6c757d;
      background-color: #f1f3f4;
    }

    .group-tab.tool.active {
      background-color: #6c757d;
      color: white;
    }

    .app-grid {
      display: none;
      grid-template-columns: repeat(3, 1fr); /* 固定为3列 */
      gap: 10px;
      margin-bottom: 20px;
    }

    .app-grid.active {
      display: grid;
    }

    .app-card {
      padding: 12px 16px;
      border: 1px solid #ddd;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.2s ease;
      background-color: #fff;
    }

    .app-card:hover {
      background-color: #f8f9fa;
      border-color: #007bff;
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .app-card.selected {
      background-color: #e3f2fd;
      border-color: #007bff;
      color: #1976d2;
    }

    .app-name {
      font-size: 14px;
      font-weight: 500;
      color: #333;
    }

    .app-card.selected .app-name {
      color: #1976d2;
    }

    #result {
      margin-top: 20px;
      max-height: 600px;
      overflow-y: auto;
    }

    .result-header {
      background-color: #f8f9fa;
      padding: 10px 15px;
      border: 1px solid #ddd;
      border-radius: 4px 4px 0 0;
      font-weight: bold;
      color: #333;
    }

    table {
      width: 100%;
      border-collapse: collapse;
      font-size: 13px;
      border: 1px solid #ddd;
      border-top: none;
    }

    th {
      background-color: #f5f5f5;
      font-weight: bold;
      padding: 8px;
      text-align: left;
      border: 1px solid #ddd;
      vertical-align: middle;
    }

    td {
      padding: 8px;
      border: 1px solid #ddd;
      vertical-align: middle;
    }

    .env-badge {
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: bold;
      text-align: center;
      display: inline-block;
      min-width: 80px;
    }

    .env-prod {
      background-color: #dc3545;
      color: white;
    }

    .env-uat {
      background-color: #ffc107;
      color: #212529;
    }

    .env-test {
      background-color: #17a2b8;
      color: white;
    }

    .env-dev {
      background-color: #28a745;
      color: white;
    }

    .env-grey {
      background-color: #6c757d;
      color: white;
    }

    .type-badge {
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: bold;
      text-align: center;
      display: inline-block;
      min-width: 60px;
    }

    .type-normal {
      background-color: #28a745;
      color: white;
    }

    .type-grey {
      background-color: #ffc107;
      color: #212529;
    }

    .jump-btn {
      display: inline-block;
      padding: 4px 12px;
      background-color: #1e88e5;
      color: white;
      text-decoration: none;
      border-radius: 3px;
      text-align: center;
      min-width: 60px;
      border: none;
      cursor: pointer;
      font-size: 12px;
    }

    .jump-btn:hover {
      background-color: #1976d2;
    }

    .no-results {
      text-align: center;
      padding: 20px;
      color: #666;
      font-size: 14px;
    }

    .loading {
      text-align: center;
      padding: 20px;
      color: #666;
      font-size: 14px;
    }

    .error {
      color: #dc3545 !important;
    }
  </style>
</head>
<body>
  <h2>TIC网站查询</h2>
  
  <div id="groupTabs" class="group-tabs">
    <!-- 分组标签将在这里动态生成 -->
  </div>

  <div id="appGrids">
    <!-- 应用网格将在这里动态生成 -->
  </div>

  <div id="result" style="display: none;">
    <!-- 查询结果将在这里显示 -->
  </div>

  <script src="ticWebsite.js"></script>
</body>
</html>