// 存储查询历史
const saveQueryHistory = async (searchTerm) => {
  const history = await chrome.storage.local.get('queryHistory') || { queryHistory: [] };
  history.queryHistory.unshift(searchTerm);
  // 只保留最近10条记录
  history.queryHistory = history.queryHistory.slice(0, 10);
  await chrome.storage.local.set(history);
};

// 加载 VM 数据
async function loadVMData() {
  try {
    const response = await fetch('../json/vm.json');
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('加载VM数据失败：', error);
    throw error;
  }
}

// 生成监控URL
function generateMonitorUrl(vmName) {
  const lowerVmName = vmName ? vmName.toLowerCase() : '';
  return `http://**************:3000/d/NjBZadTIk/os-monitor-details?orgId=1&var-ident=${lowerVmName}.apac.global.sgs.com&from=now-15m&to=now`;
}

// 查询数据
async function queryData(searchTerm) {
  try {
    const vmData = await loadVMData();

    if (!searchTerm) {
      return vmData;
    }

    const searchTermLower = searchTerm.toLowerCase();

    return vmData.filter(item =>
      (item.vmName && item.vmName.toLowerCase().includes(searchTermLower)) ||
      (item.vmHost && item.vmHost.includes(searchTermLower)) ||
      (item.appName && item.appName.toLowerCase().includes(searchTermLower))
    );
  } catch (error) {
    console.error('查询出错：', error);
    throw error;
  }
}

// 格式化显示结果
function formatResults(results) {
  if (results.length === 0) {
    return '<p>未找到匹配的数据</p>';
  }

  return `
    <table>
      <thead>
        <tr>
          <th>机器名</th>
          <th>IP地址</th>
          <th>应用名称</th>
          <th>环境</th>
          <th style="width: 150px;">操作</th>
        </tr>
      </thead>
      <tbody>
        ${results.map(item => `
          <tr>
            <td>${item.vmName || '-'}</td>
            <td>${item.vmHost || '-'}</td>
            <td>${item.appName || '-'}</td>
            <td>${item.envName || '-'}</td>
            <td style="text-align: center;">
              <a href="${generateMonitorUrl(item.vmName)}"
                 target="_blank"
                 class="monitor-link">
                打开监控页面
              </a>
            </td>
          </tr>
        `).join('')}
      </tbody>
    </table>
  `;
}

// 监听表单提交事件
document.getElementById('queryForm').addEventListener('submit', async (event) => {
  event.preventDefault();

  const searchTerm = document.getElementById('searchInput').value.trim();

  try {
    const resultDiv = document.getElementById('result');
    resultDiv.innerHTML = '查询中...';

    const results = await queryData(searchTerm);

    resultDiv.innerHTML = formatResults(results);
  } catch (error) {
    document.getElementById('result').innerHTML = `
      <p style="color: red;">查询失败: ${error.message}</p>
    `;
  }
});