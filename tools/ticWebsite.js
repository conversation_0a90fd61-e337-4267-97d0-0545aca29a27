// 分组名称映射
const GROUP_NAMES = {
  'user-pc': '用户PC端',
  'cs-pc': '客服端',
  'user-mobile': '用户移动端',
  'tool': '工具'
};

let currentApp = null;
let currentGroup = null;

// 加载网站分组数据
async function loadWebsiteGroupData() {
  try {
    const response = await fetch('../json/webSiteGroup.json');
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('加载网站分组数据失败：', error);
    throw error;
  }
}

// 加载 TIC 网站数据
async function loadTicWebsiteData() {
  try {
    const response = await fetch('../json/ticWebSite.json');
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('加载TIC网站数据失败：', error);
    throw error;
  }
}

// 按分组整理数据
function groupWebsiteData(data) {
  const grouped = {};
  
  data.forEach(item => {
    if (!grouped[item.group]) {
      grouped[item.group] = [];
    }
    grouped[item.group].push(item);
  });

  // 按num排序
  Object.keys(grouped).forEach(group => {
    grouped[group].sort((a, b) => a.num - b.num);
  });

  return grouped;
}

// 渲染分组标签
function renderGroupTabs(groupedData) {
  const groupTabs = document.getElementById('groupTabs');
  
  const html = Object.keys(groupedData).map(group => {
    const groupName = GROUP_NAMES[group] || group;
    const apps = groupedData[group];
    
    return `
      <div class="group-tab ${group}" data-group="${group}">
        ${groupName} (${apps.length})
      </div>
    `;
  }).join('');

  groupTabs.innerHTML = html;
}

// 渲染应用网格
function renderAppGrids(groupedData) {
  const appGrids = document.getElementById('appGrids');
  
  const html = Object.keys(groupedData).map(group => {
    const apps = groupedData[group];
    
    return `
      <div class="app-grid" id="grid-${group}" data-group="${group}">
        ${apps.map(app => `
          <div class="app-card" data-short="${app.short}" data-title="${app.title}">
            <div class="app-name">${app.title}(${app.short})</div>
          </div>
        `).join('')}
      </div>
    `;
  }).join('');

  appGrids.innerHTML = html;
}

// 切换分组
function switchGroup(groupId) {
  // 更新标签状态
  document.querySelectorAll('.group-tab').forEach(tab => {
    tab.classList.remove('active');
  });
  document.querySelector(`[data-group="${groupId}"]`).classList.add('active');

  // 更新应用网格显示
  document.querySelectorAll('.app-grid').forEach(grid => {
    grid.classList.remove('active');
  });
  document.getElementById(`grid-${groupId}`).classList.add('active');

  // 清除应用选中状态和结果
  document.querySelectorAll('.app-card').forEach(card => {
    card.classList.remove('selected');
  });
  document.getElementById('result').style.display = 'none';

  currentGroup = groupId;
  currentApp = null;
}

// 选择应用
async function selectApp(short, title, element) {
  // 移除之前的选中状态
  document.querySelectorAll('.app-card').forEach(card => {
    card.classList.remove('selected');
  });
  
  // 添加当前选中状态
  element.classList.add('selected');
  
  currentApp = { short, title };
  
  try {
    const resultDiv = document.getElementById('result');
    resultDiv.style.display = 'block';
    resultDiv.innerHTML = '<div class="loading">查询中...</div>';

    const websiteData = await loadTicWebsiteData();
    const results = websiteData.filter(item => item.short === short);

    if (results.length === 0) {
      resultDiv.innerHTML = `
        <div class="result-header">${title} - 未找到相关网站</div>
        <div class="no-results">该应用暂无配置的网站环境</div>
      `;
    } else {
      resultDiv.innerHTML = `
        <div class="result-header">${title} - 网站环境列表</div>
        ${formatWebsiteResults(results)}
      `;
    }
  } catch (error) {
    document.getElementById('result').innerHTML = `
      <div class="result-header">查询失败</div>
      <div class="no-results error">查询失败: ${error.message}</div>
    `;
  }
}

// 获取环境样式类
function getEnvClass(env) {
  if (!env) return '';
  
  const envLower = env.toLowerCase();
  if (envLower.includes('生产')) {
    return 'env-prod';
  } else if (envLower.includes('uat')) {
    return 'env-uat';
  } else if (envLower.includes('test')) {
    return 'env-test';
  } else if (envLower.includes('dev')) {
    return 'env-dev';
  } else if (envLower.includes('灰度')) {
    return 'env-grey';
  }
  return '';
}

// 格式化显示结果
function formatWebsiteResults(results) {
  return `
    <table>
      <thead>
        <tr>
          <th>网站名称</th>
          <th>环境</th>
          <th>简称</th>
          <th>类型</th>
          <th style="width: 100px; text-align: center;">操作</th>
        </tr>
      </thead>
      <tbody>
        ${results.map((item, index) => `
          <tr>
            <td>${item.title || '-'}</td>
            <td>
              ${item.env ? `
                <span class="env-badge ${getEnvClass(item.env)}">
                  ${item.env}
                </span>
              ` : '-'}
            </td>
            <td>${item.short || '-'}</td>
            <td>
              <span class="type-badge ${item.type === 'grey' ? 'type-grey' : 'type-normal'}">
                ${item.type === 'grey' ? '灰度环境' : '非灰度环境'}
              </span>
            </td>
            <td style="text-align: center;">
              ${item.url ? `
                <button class="jump-btn" data-url="${item.url}" data-index="${index}">
                  跳转网站
                </button>
              ` : '-'}
            </td>
          </tr>
        `).join('')}
      </tbody>
    </table>
  `;
}

// 打开网站
function openWebsite(url) {
  try {
    window.open(url, '_blank', 'noopener,noreferrer');
  } catch (error) {
    console.error('打开网站失败：', error);
    // 备用方案
    const link = document.createElement('a');
    link.href = url;
    link.target = '_blank';
    link.rel = 'noopener noreferrer';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
}

// 初始化事件监听器
function initEventListeners() {
  // 分组标签点击事件
  document.getElementById('groupTabs').addEventListener('click', (event) => {
    if (event.target.classList.contains('group-tab')) {
      const groupId = event.target.getAttribute('data-group');
      switchGroup(groupId);
    }
  });

  // 应用卡片点击事件
  document.getElementById('appGrids').addEventListener('click', (event) => {
    const appCard = event.target.closest('.app-card');
    if (appCard) {
      const short = appCard.getAttribute('data-short');
      const title = appCard.getAttribute('data-title');
      selectApp(short, title, appCard);
    }
  });

  // 跳转按钮点击事件
  document.getElementById('result').addEventListener('click', (event) => {
    if (event.target.classList.contains('jump-btn')) {
      const url = event.target.getAttribute('data-url');
      if (url) {
        openWebsite(url);
      }
    }
  });
}

// 初始化页面
async function initPage() {
  try {
    const groupData = await loadWebsiteGroupData();
    const groupedData = groupWebsiteData(groupData);
    renderGroupTabs(groupedData);
    renderAppGrids(groupedData);
    initEventListeners();
    
    // 默认选中第一个分组
    const firstGroup = Object.keys(groupedData)[0];
    if (firstGroup) {
      switchGroup(firstGroup);
    }
  } catch (error) {
    document.getElementById('groupTabs').innerHTML = `
      <div class="error">加载失败: ${error.message}</div>
    `;
  }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', initPage);
