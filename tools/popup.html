<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <title>信息查询插件</title>
  <style>
    body {
      margin: 0;
      padding: 15px;
      font-family: Arial, sans-serif;
      font-size: 14px;
    }

    h2 {
      margin: 0 0 15px 0;
      font-size: 16px;
      color: #333;
    }

    .search-container {
      display: flex;
      gap: 10px;
      margin-bottom: 15px;
    }

    .form-group {
      flex: 1;
    }

    label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
      color: #666;
    }

    input {
      width: 100%;
      padding: 6px 8px;
      box-sizing: border-box;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
    }

    input:focus {
      border-color: #1e88e5;
      outline: none;
    }

    button {
      padding: 6px 20px;
      background-color: #4CAF50;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      margin-top: 24px;
    }

    button:hover {
      background-color: #45a049;
    }

    #result {
      margin-top: 15px;
      max-height: 600px;
      overflow-y: auto;
    }

    table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 10px;
      font-size: 13px;
    }

    th {
      background-color: #f5f5f5;
      font-weight: bold;
      padding: 8px;
      text-align: left;
      border: 1px solid #ddd;
    }

    td {
      padding: 8px;
      border: 1px solid #ddd;
    }

    .monitor-link {
      display: inline-block;
      padding: 4px 12px;
      background-color: #1e88e5;
      color: white;
      text-decoration: none;
      border-radius: 3px;
      text-align: center;
      min-width: 90px;
    }

    .monitor-link:hover {
      background-color: #1976d2;
    }
  </style>
</head>
<body>
  <h2>信息查询</h2>
  <form id="queryForm">
    <div class="search-container">
      <div class="form-group">
        <label for="searchInput">请输入机器名/IP/应用名称：</label>
        <input type="text"
               id="searchInput"
               name="searchInput"
               placeholder="请输入机器名/IP/应用名称">
      </div>
      <button type="submit">查询</button>
    </div>
  </form>
  <div id="result"></div>
  <script src="popup.js"></script>
</body>
</html>
