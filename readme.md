```

```
## Description
作为SGS内部查询插件
### release
#### 版本信息
- **版本号**: 1.4
- **发布日期**: 2025年07月15日
- **新增内容**: 新增TIC网站查询

---
#### 新增功能
##### 1. 功能一：新增TIC网站检索跳转功能
- **描述**: 列举TIC网站信息
- **亮点**:
  - 帮助开发、QA、BA人员快速查询TIC网站地址

### release
#### 版本信息
- **版本号**: 1.3
- **发布日期**: 2025年06月28日
- **新增内容**: 新增大版本发布工具，代码分析工具

---
#### 新增功能
##### 1. 功能一：新增大版本发布工具
- **描述**: 根据大版本发布的checkpoint检查发布信息
- **亮点**:
  - 帮助开发、QA人员查看割接计划，自动核对版本信息

##### 2. 功能二：新增代码分析工具
- **描述**: 新增分析工程代码
- **亮点**:
  - 帮助开发人员分析复杂代码，将代码流程生成图形方便理解

### release
#### 版本信息
- **版本号**: 1.2
- **发布日期**: 2025年04月23日
- **新增内容**: 新增发布平台连接，新增版本分支管理

---
#### 新增功能
##### 1. 功能一：新增发布平台连接
- **描述**: 新增发布平台连接跳转
- **亮点**:
  - 帮助开发人员快速跳转到发布 平台

##### 2. 功能二：新增gitlab分支查询
- **描述**: 新增根据release查询版本分支
- **要求**:
  - gitlab分支名称必须以ISSUE号开头，如: TIC-123 XXX
- **亮点**:
  - 帮助开发人员检查分支信息，后续版本校验核对代码合并情况

#### 版本信息
- **版本号**: 1.1
- **发布日期**: 2025年04月21日
- **新增内容**: 新增开发工具JSON转换器，支持JSON格式数据转换

---
#### 新增功能
##### 1. 功能一：开发工具JSON转换器
- **描述**: 新增开发工具JSON转换器
- **亮点**:
    - 帮助开发人员/需求人员/运维人员快速解析JSON信息


##### 2. 功能二：日志分析工具
- **描述**: 通过大模型分析日志信息并给出修改意见
- **亮点**:
    - 帮助开发人员快速定位问题

#### 版本信息
- **版本号**: 1.0
- **发布日期**: 2025年04月17日
- **更新内容**: 新增主机资源查询

---
#### 新增功能
##### 1. 功能一：根据主机名称/IP/应用名称快速查询主机资源
- **描述**: 新增主机资源查询功能
- **亮点**:
    - 帮助开发人员/运维人员可快速查找应用主机信息，并查看主机资源


### Usage
1. 安装插件
2. 点击插件图标
3. 输入查询条件
4. 点击查询按钮
5. 查看查询结果
6. 点击打开监控页面按钮，跳转到监控页面
```