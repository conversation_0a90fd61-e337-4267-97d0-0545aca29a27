// 全局变量
let projectData = [];
let sprintInfo = null;

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', async () => {
    // 加载项目数据
    await loadProjectData();
    
    // 设置查询按钮事件
    document.getElementById('searchBtn').addEventListener('click', querySprintTime);
    
    // 设置模态框关闭事件
    document.querySelector('.close').addEventListener('click', () => {
        document.getElementById('issueModal').style.display = 'none';
    });
    
    // 点击模态框外部关闭
    window.addEventListener('click', (event) => {
        const modal = document.getElementById('issueModal');
        if (event.target === modal) {
            modal.style.display = 'none';
        }
    });
});

// 加载项目数据
async function loadProjectData() {
    try {
        const response = await fetch('../json/project.json');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        projectData = await response.json();
        
        // 填充项目选择下拉框
        const projectSelect = document.getElementById('projectKey');
        
        projectData.forEach(project => {
            if (project.projectGroup) {
                const option = document.createElement('option');
                option.value = project.projectGroup;
                option.textContent = project.projectGroup;
                option.dataset.boardId = project.boardId; // 保存boardId到dataset
                projectSelect.appendChild(option);
            }
        });
    } catch (error) {
        console.error('加载项目数据失败:', error);
        showError('加载项目数据失败: ' + error.message);
    }
}

// 获取Sprint工作日信息
async function getSprintWorkday(boardId) {
    try {
        const response = await fetch('http://10.169.128.35:8611/business/api/v1/tools/jira/calcSprintWorkday', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                boardId: boardId
            })
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        
        if (data.resultCode === '0') {
            return data.data;
        } else {
            throw new Error(data.resultMsg || '获取Sprint工作日信息失败');
        }
    } catch (error) {
        console.error('获取Sprint工作日信息失败:', error);
        throw error;
    }
}

// 查询Sprint工时
async function querySprintTime() {
    const projectKeySelect = document.getElementById('projectKey');
    const projectKey = projectKeySelect.value;
    const selectedOption = projectKeySelect.options[projectKeySelect.selectedIndex];
    const boardId = selectedOption.dataset.boardId;
    
    if (!projectKey) {
        showError('请选择项目Key');
        return;
    }
    
    if (!boardId) {
        showError('所选项目没有关联的看板ID');
        return;
    }
    
    const resultDiv = document.getElementById('result');
    resultDiv.innerHTML = '<div class="loading">查询中，请稍候...</div>';
    
    try {
        // 获取Sprint工作日信息
        sprintInfo = await getSprintWorkday(boardId);
        
        // 获取Sprint工时数据
        const response = await fetch('http://10.169.128.35:8611/business/api/v1/tools/jira/getSprintTime', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                projectGroup: projectKey
            })
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        
        if (data.resultCode === '0') {
            displayResults(data.data, sprintInfo);
        } else {
            throw new Error(data.resultMsg || '查询失败');
        }
    } catch (error) {
        console.error('查询Sprint工时失败:', error);
        showError('查询Sprint工时失败: ' + error.message);
    }
}

// 显示查询结果
function displayResults(data, sprintInfo) {
    const resultDiv = document.getElementById('result');
    
    if (!data || data.length === 0) {
        resultDiv.innerHTML = '<p>没有找到相关数据</p>';
        return;
    }
    
    // 格式化日期
    const formatDate = (dateString) => {
        if (!dateString) return '-';
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    };
    
    // 显示Sprint信息
    let html = `
        <div class="sprint-info">
            <h2>
                <a href="${sprintInfo.self}" target="_blank">${sprintInfo.name || '未知Sprint'}</a>
            </h2>
            <p>开始时间: ${formatDate(sprintInfo.startDate)} | 结束时间: ${formatDate(sprintInfo.endDate)}</p>
            <p>Sprint目标: ${sprintInfo.goal || '无'}</p>
        </div>
        
        <table>
            <thead>
                <tr>
                    <th>负责人</th>
                    <th>总工时(小时)</th>
                    <th>计算工时(小时) <span class="tooltip">ℹ️<span class="tooltiptext">计算方式：总工时 * 80%，再除以110%（buffer）</span></span></th>
                    <th>预估工时(小时)</th>
                    <th>剩余工时(小时)</th>
                    <th>本Sprint Log工时(小时)</th>
                    <th>任务总Log工时(小时)</th>
                    <th>任务</th>
                </tr>
            </thead>
            <tbody>
    `;
    
    // 总工作时间
    const totalWorkHour = sprintInfo.workHour || 0;
    
    data.forEach((item, index) => {
        // 计算工时 = (总工时*0.8)/1.1
        const calculatedHour = totalWorkHour ? ((totalWorkHour * 0.8) / 1.1).toFixed(1) : '0';
        
        html += `
            <tr>
                <td>${item.assignee || '-'}</td>
                <td>${totalWorkHour || '0'}</td>
                <td>${calculatedHour}</td>
                <td>${item.totalHour || '0'}</td>
                <td>${item.remainHour || '0'}</td>
                <td>${item.sprintHour || '0'}</td>
                <td>${item.costHour || '0'}</td>
                <td>
                    <button class="view-more" data-index="${index}">查看更多</button>
                </td>
            </tr>
        `;
    });
    
    html += `
            </tbody>
        </table>
    `;
    
    resultDiv.innerHTML = html;
    
    // 保存数据到全局变量，以便在点击"查看更多"时使用
    window.sprintTimeData = data;
    
    // 添加"查看更多"按钮点击事件
    document.querySelectorAll('.view-more').forEach(button => {
        button.addEventListener('click', function() {
            const index = this.getAttribute('data-index');
            showIssueDetails(window.sprintTimeData[index].issues);
        });
    });
}

// 显示Issue详情
function showIssueDetails(issues) {
    const issueDetailsDiv = document.getElementById('issueDetails');
    const modal = document.getElementById('issueModal');
    
    if (!issues || issues.length === 0) {
        issueDetailsDiv.innerHTML = '<p>没有任务数据</p>';
    } else {
        let html = `
            <table style="table-layout: fixed; width: 100%;">
                <thead>
                    <tr>
                        <th style="width: 120px;">Issue Key</th>
                        <th style="width: 25%;">摘要</th>
                        <th style="width: 80px;">类型</th>
                        <th style="width: 80px;">状态</th>
                        <th style="width: 100px;">负责人</th>
                        <th style="width: 15%;">组件</th>
                        <th style="width: 100px;">总工时(小时)</th>
                        <th style="width: 100px;">剩余工时(小时)</th>
                        <th style="width: 100px;">已消耗工时(小时)</th>
                    </tr>
                </thead>
                <tbody>
        `;
        
        issues.forEach(issue => {
            // 计算小时数
            const totalHours = (issue.totalTimes / 3600).toFixed(1);
            const remainHours = (issue.remainTimes / 3600).toFixed(1);
            const costHours = (issue.costTimes / 3600).toFixed(1);
            
            html += `
                <tr>
                    <td style="overflow: hidden; text-overflow: ellipsis;"><a href="${issue.self}" target="_blank">${issue.key}</a></td>
                    <td style="overflow: hidden; text-overflow: ellipsis;">${issue.summary || '-'}</td>
                    <td style="overflow: hidden; text-overflow: ellipsis;">${issue.issueType || '-'}</td>
                    <td style="overflow: hidden; text-overflow: ellipsis;">${issue.status || '-'}</td>
                    <td style="overflow: hidden; text-overflow: ellipsis;">${issue.assignee || '-'}</td>
                    <td style="overflow: hidden; text-overflow: ellipsis;">${Array.isArray(issue.components) ? issue.components.join(', ') : '-'}</td>
                    <td style="text-align: center;">${totalHours}</td>
                    <td style="text-align: center;">${remainHours}</td>
                    <td style="text-align: center;">${costHours}</td>
                </tr>
            `;
        });
        
        html += `
                </tbody>
            </table>
        `;
        
        issueDetailsDiv.innerHTML = html;
    }
    
    modal.style.display = 'block';
}

// 显示错误信息
function showError(message) {
    const resultDiv = document.getElementById('result');
    resultDiv.innerHTML = `<div class="error">${message}</div>`;
}