<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>当前Sprint工时</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            font-size: 16px; /* 增大基础字体大小 */
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            margin-top: 0;
        }
        .search-form {
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        select, button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #45a049;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
            font-size: 15px; /* 增大表格内容字体 */
        }
        th {
            white-space: nowrap;
            background-color: #f2f2f2;
            font-weight: bold;
            font-size: 16px; /* 增大表头字体 */
        }
        tr:hover {
            background-color: #f9f9f9;
        }
        .view-more {
            background-color: #2196F3;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
        }
        .view-more:hover {
            background-color: #0b7dda;
        }
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        .modal-content {
            background-color: #fff;
            margin: 5% auto;
            padding: 20px;
            border-radius: 8px;
            width: 90%; /* 增大弹层宽度，从80%到90% */
            max-width: 1200px; /* 增大最大宽度，从800px到1200px */
            max-height: 85vh; /* 增大最大高度，从80vh到85vh */
            overflow-y: auto;
        }
        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        .close:hover {
            color: #000;
        }
        .loading {
            text-align: center;
            padding: 20px;
            font-style: italic;
            color: #666;
        }
        .error {
            color: #f44336;
            padding: 10px;
            background-color: #ffebee;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        .sprint-info {
            margin-top: 20px;
            padding: 15px;
            background-color: #e8f5e9;
            border-radius: 4px;
            border-left: 4px solid #4CAF50;
            font-size: 16px; /* 增大Sprint信息字体 */
        }
        .sprint-info h2 {
            margin-top: 0;
            color: #2e7d32;
            font-size: 20px; /* 增大Sprint标题字体 */
        }
        .sprint-info a {
            color: #2e7d32;
            text-decoration: none;
        }
        .sprint-info a:hover {
            text-decoration: underline;
        }
        .sprint-info p {
            margin: 5px 0;
            color: #555;
        }
        /* 气泡提示样式 - 修复换行问题 */
        .tooltip {
            position: relative;
            display: inline-block;
            cursor: help;
            margin-left: 5px;
            color: #2196F3;
        }
        
        .tooltip .tooltiptext {
            visibility: hidden;
            width: 300px;
            background-color: #333;
            color: #fff;
            text-align: left; /* 左对齐更适合多行文本 */
            border-radius: 6px;
            padding: 10px;
            position: absolute;
            z-index: 1;
            bottom: 125%;
            left: 50%;
            margin-left: -150px;
            opacity: 0;
            transition: opacity 0.3s;
            font-weight: normal;
            font-size: 14px;
            line-height: 1.5;
            white-space: normal; /* 允许文本换行 */
            word-wrap: break-word; /* 确保长单词也能换行 */
            overflow-wrap: break-word; /* 现代浏览器的换行属性 */
        }
        
        .tooltip .tooltiptext::after {
            content: "";
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -5px;
            border-width: 5px;
            border-style: solid;
            border-color: #333 transparent transparent transparent;
        }
        
        .tooltip:hover .tooltiptext {
            visibility: visible;
            opacity: 1;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>当前Sprint工时</h1>
        
        <div class="search-form">
            <label for="projectKey">项目Key:</label>
            <select id="projectKey">
                <option value="">请选择项目</option>
                <!-- 项目选项将通过JavaScript动态加载 -->
            </select>
            <button id="searchBtn">查询</button>
        </div>
        
        <div id="result">
            <!-- 查询结果将显示在这里 -->
        </div>
    </div>
    
    <!-- 模态框用于显示详细信息 -->
    <div id="issueModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>Issue详情</h2>
            <div id="issueDetails"></div>
        </div>
    </div>
    
    <script src="sprintTime.js"></script>
</body>
</html>