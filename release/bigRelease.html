
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>大版本发布</title>
    <style>
        body {
            margin: 0;
            padding: 15px;
            font-family: Arial, sans-serif;
            font-size: 14px;
        }

        h2 {
            margin: 0 0 20px 0;
            font-size: 16px;
            color: #333;
        }

        h3 {
            margin: 0 0 15px 0;
            font-size: 15px;
            color: #444;
        }

        .form-container {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
        }

        .form-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        label {
            white-space: nowrap;
            font-weight: bold;
            color: #666;
        }

        .select-container {
            position: relative;
            width: 200px;
        }

        input[type="text"] {
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        input[type="text"]:focus {
            border-color: #1e88e5;
            outline: none;
        }

        button {
            padding: 8px 20px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            white-space: nowrap;
        }

        button:hover {
            background-color: #45a049;
        }

        .options-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            max-height: 200px;
            overflow-y: auto;
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-top: 4px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            z-index: 1000;
            display: none;
        }

        .option-item {
            padding: 8px;
            cursor: pointer;
        }

        .option-item:hover {
            background-color: #f5f5f5;
        }

        #releaseQueryResult {
            margin-top: 20px;
        }

        .error {
            color: #f44336;
            padding: 15px;
            background-color: #ffebee;
            border-radius: 4px;
            margin: 10px 0;
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
    </style>
</head>
<body>
    <h2>大版本发布</h2>
    <div class="release-query-section">
        <h3>Release查询</h3>
        <form id="releaseQueryForm" class="form-container">
            <div class="form-group">
                <label for="releaseQueryInput">选择Release版本：</label>
                <div class="select-container">
                    <input type="text"
                        id="releaseQueryInput"
                        placeholder="输入或选择Release版本"
                        autocomplete="off">
                    <div id="releaseQueryOptions" class="options-dropdown"></div>
                </div>
            </div>
            <div class="form-group selected-versions-container">
                <label>已选版本：</label>
                <div id="selectedReleases" class="selected-releases"></div>
            </div>
            <div class="form-group">
                <label class="form-label">环境选择：</label>
                <div class="radio-group">
                    <label class="radio-label">
                        <input type="radio" name="environment" id="envUat" value="uat"> UAT
                    </label>
                    <label class="radio-label">
                        <input type="radio" name="environment" id="envProd" value="prod"> PROD
                    </label>
                </div>
            </div>
            <button type="submit">查询</button>
        </form>
        <div id="releaseQueryResult"></div>
    </div>

    <script src="bigRelease.js"></script>
</body>
</html>