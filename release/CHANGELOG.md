# Release查询功能更新日志

## 新增功能：历史release版本查询

### 功能描述
在"Release查询"二级菜单中新增了"历史release版本"功能，用户在选择Release版本号后点击查询，会在发布清单和发布检查清单之间显示历史release版本信息。

### 实现细节

#### 1. 新增API接口调用
- **接口地址**: `http://10.169.128.35:8611/business/api/v1/tools/jira/getChangeLog`
- **请求方法**: POST
- **请求参数**: 
  ```json
  {
    "releaseName": "TIC生产发布-250724"
  }
  ```
- **返回参数示例**:
  ```json
  {
    "data": [
      "TIC生产发布-250724（智能客服上线）",
      "智能客服上线",
      "172WK2待排",
      "TIC生产发布-250612",
      "TIC生产发布-250722",
      "TIC生产发布-250620",
      "TIC生产发布-250522",
      "TIC UAT发布-250403",
      "TIC生产发布-250724（智能客服+MIN KJ上线）",
      "TIC UAT发布-250716",
      "TIC UAT发布-250714",
      "智能客服第一期sp143wk2",
      "TIC UAT发布-250415",
      "TIC生产发布-250715",
      "TIC生产发布-250327",
      "TIC生产发布-250724",
      "TIC生产发布-250408",
      "TIC生产发布-250717"
    ],
    "resultCode": "0",
    "resultMsg": "成功"
  }
  ```

#### 2. 代码修改

##### 新增函数
- `getChangeLog(releaseName)`: 调用历史版本查询接口
- `loadHistoryVersions(releaseName)`: 加载并显示历史版本信息

##### 修改函数
- `formatResults(response)`: 在发布清单和发布检查清单之间添加历史版本区域
- 主查询事件处理函数: 在查询Release信息后异步加载历史版本

#### 3. UI界面更新

##### HTML结构
在查询结果中新增了"历史release版本"区域，包含：
- 区域标题
- 加载状态提示
- 历史版本列表容器

##### CSS样式
新增了以下样式类：
- `.history-versions-container`: 历史版本容器样式
- `.history-versions-list`: 历史版本列表网格布局
- `.history-version-item`: 单个历史版本项样式
- `.version-index`: 版本序号圆形标签样式
- `.version-name`: 版本名称样式

#### 4. 功能特点

1. **异步加载**: 历史版本信息在主查询完成后异步加载，不影响主要功能的响应速度
2. **错误处理**: 包含完善的错误处理机制，当接口调用失败时显示友好的错误信息
3. **响应式布局**: 历史版本列表采用网格布局，自适应不同屏幕尺寸
4. **用户体验**: 
   - 加载过程中显示"加载历史版本中..."提示
   - 版本列表带有序号标识，便于查看
   - 鼠标悬停效果增强交互体验

#### 5. 测试方法

1. 启动本地服务器：
   ```bash
   cd release
   python -m http.server 8080
   ```

2. 在浏览器中访问：`http://localhost:8080/release.html`

3. 选择一个Release版本号并点击查询

4. 查看结果页面中是否显示"历史release版本"区域

### 技术栈
- 前端：原生JavaScript + HTML + CSS
- 接口：RESTful API
- 布局：CSS Grid + Flexbox

### 兼容性
- 支持现代浏览器（Chrome、Firefox、Safari、Edge）
- 响应式设计，支持桌面和移动设备
