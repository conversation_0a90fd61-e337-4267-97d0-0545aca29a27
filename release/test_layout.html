<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>布局测试 - Release查询</title>
    <style>
        body {
            margin: 0;
            padding: 15px;
            font-family: Arial, sans-serif;
            font-size: 14px;
        }

        .release-section {
            margin-bottom: 30px;
            border: 1px solid #ddd;
            border-radius: 6px;
            overflow: hidden;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .section-title {
            margin: 0;
            padding: 15px;
            background-color: #f5f5f5;
            border-bottom: 1px solid #ddd;
            font-size: 18px;
            color: #333;
        }

        .history-versions-container {
            padding: 15px;
        }
        
        .history-versions-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }
        
        .history-version-item {
            display: flex;
            align-items: center;
            padding: 12px 15px;
            background: #f9f9f9;
            border: 1px solid #eee;
            border-radius: 4px;
            transition: background-color 0.2s;
        }
        
        .history-version-item:hover {
            background-color: #f0f0f0;
        }
        
        .version-index {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            background: #4CAF50;
            color: white;
            border-radius: 50%;
            font-size: 12px;
            font-weight: bold;
            margin-right: 12px;
            flex-shrink: 0;
        }
        
        .version-name {
            flex: 1;
            font-size: 14px;
            color: #333;
            word-break: break-word;
        }

        .placeholder {
            padding: 20px;
            background: #f9f9f9;
            color: #666;
            text-align: center;
            font-style: italic;
        }
    </style>
</head>
<body>
    <h2>布局测试 - Release查询结果</h2>
    
    <!-- 历史release版本区域（现在在最前面） -->
    <div class="release-section">
        <h2 class="section-title">历史release版本</h2>
        <div class="history-versions-container">
            <div class="history-versions-list">
                <div class="history-version-item">
                    <span class="version-index">1</span>
                    <span class="version-name">TIC生产发布-250724（智能客服上线）</span>
                </div>
                <div class="history-version-item">
                    <span class="version-index">2</span>
                    <span class="version-name">智能客服上线</span>
                </div>
                <div class="history-version-item">
                    <span class="version-index">3</span>
                    <span class="version-name">172WK2待排</span>
                </div>
                <div class="history-version-item">
                    <span class="version-index">4</span>
                    <span class="version-name">TIC生产发布-250612</span>
                </div>
                <div class="history-version-item">
                    <span class="version-index">5</span>
                    <span class="version-name">TIC生产发布-250722</span>
                </div>
                <div class="history-version-item">
                    <span class="version-index">6</span>
                    <span class="version-name">TIC生产发布-250620</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 发布清单部分（现在在第二位） -->
    <div class="release-section">
        <h2 class="section-title">发布清单</h2>
        <div class="placeholder">
            发布清单内容区域
            <br>（包含Issue列表和Git仓库信息）
        </div>
    </div>

    <!-- 发布检查清单部分（保持在最后） -->
    <div class="release-section">
        <h2 class="section-title">发布检查清单</h2>
        <div class="placeholder">
            发布检查清单内容区域
            <br>（包含日常Release发布和大版本割接计划）
        </div>
    </div>

</body>
</html>
