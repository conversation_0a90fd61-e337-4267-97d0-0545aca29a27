// 存储所有版本号
let allVersions = [];

// 加载版本号列表
async function loadReleaseVersions() {
    try {
        const response = await fetch('http://10.169.128.35:8611/business/api/v1/tools/jira/getReleases', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                limit: 50,
                projectGroup: "TIC"
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        
        allVersions = Array.isArray(data) ? data : (data.data || data.versions || []);
        
        if (!Array.isArray(allVersions)) {
            throw new Error('无效的数据格式');
        }

        updateVersionOptions(allVersions);
    } catch (error) {
        console.error('加载版本号列表失败：', error);
        document.getElementById('releaseQueryResult').innerHTML = `<div class="error">加载版本号列表失败: ${error.message}</div>`;
    }
}

// 更新版本号选项
function updateVersionOptions(versions) {
    const optionsContainer = document.getElementById('releaseQueryOptions');
    optionsContainer.innerHTML = '';
    
    versions.forEach(version => {
        const versionName = version.name || version.versionName || version.version || version;
        const option = document.createElement('div');
        option.className = 'option-item';
        option.textContent = versionName;
        option.onclick = () => selectVersion(versionName);
        optionsContainer.appendChild(option);
    });
}

// 根据输入筛选版本号
function filterVersions(searchText) {
    if (!searchText) {
        updateVersionOptions(allVersions);
        return;
    }
    
    const filteredVersions = allVersions.filter(version => {
        const versionName = version.name || version.versionName || version.version || version;
        return versionName.toLowerCase().includes(searchText.toLowerCase());
    });
    
    updateVersionOptions(filteredVersions);
}

// 选择版本号
function selectVersion(version) {
    // 清空输入框
    const searchInput = document.getElementById('releaseQueryInput');
    searchInput.value = '';
    
    // 隐藏下拉选项
    document.getElementById('releaseQueryOptions').style.display = 'none';
    
    // 检查是否已经选择过该版本
    const selectedContainer = document.getElementById('selectedReleases');
    const existingTags = selectedContainer.querySelectorAll('.release-tag');
    for (let tag of existingTags) {
        if (tag.dataset.version === version) {
            return; // 已存在，不重复添加
        }
    }
    
    // 创建选中标签
    const tag = document.createElement('div');
    tag.className = 'release-tag';
    tag.dataset.version = version;
    tag.innerHTML = `
        <span>${version}</span>
        <button type="button" class="remove-tag">×</button>
    `;
    
    // 添加移除事件
    tag.querySelector('.remove-tag').addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        tag.remove();
    });
    
    // 添加到已选容器
    selectedContainer.appendChild(tag);
}


// 查询Release流程信息
async function queryReleaseProcess(versions, env = 'uat') {
    try {
        const response = await fetch('http://10.169.128.35:8611/business/api/v1/release/release/queryProcess', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                releaseNames: versions, // 修改为数组参数
                env: env
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        
        if (result.resultCode !== "0") {
            throw new Error(`API error: ${result.resultMsg}`);
        }
        
        return result.data;
    } catch (error) {
        console.error('查询Release流程失败：', error);
        throw error;
    }
}

// 格式化查询结果
function formatResults(data) {
    if (!data || Object.keys(data).length === 0) {
        return '<div class="error">未找到相关Release信息</div>';
    }

    // 这里可以根据大版本发布的需求自定义结果展示
    let html = `
        <div class="release-info-section">
            <h3>Release信息: ${data.releaseName || data.name || '未知'}</h3>
            <div class="release-details">
                <p><strong>状态:</strong> ${data.status || '未知'}</p>
                <p><strong>开始日期:</strong> ${data.startDate || '未知'}</p>
                <p><strong>发布日期:</strong> ${data.releaseDate || '未知'}</p>
                <p><strong>描述:</strong> ${data.description || '无'}</p>
            </div>
        </div>
    `;

    return html;
}

// 格式化流程数据
function formatProcessData(processData) {
    if (!processData || !Array.isArray(processData) || processData.length === 0) {
        return '<div class="error">未找到相关流程信息</div>';
    }

    let html = '<div class="release-process-container">';
    
    // 遍历第一层数据（割接前、正式割接）
    processData.forEach(phase => {
        // 添加校验结果标识
        let checkResultHtml = '';
        if (phase.checkResult !== undefined && phase.checkResult !== null) {
            if (phase.checkResult === true) {
                checkResultHtml = '<span class="check-result success">Done</span>';
            } else {
                checkResultHtml = `<span class="check-result error">校验不通过${phase.checkMsg ? ': ' + phase.checkMsg : ''}</span>`;
            }
        }
        
        html += `
            <div class="process-phase">
                <div class="section-header phase-title">
                    <h3>${phase.name} ${checkResultHtml}</h3>
                </div>
                <div class="phase-content">
        `;
        
        // 遍历第二层数据（功能测试、代码修改影响范围等）
        if (phase.children && Array.isArray(phase.children) && phase.children.length > 0) {
            phase.children.forEach(task => {
                // 添加校验结果标识
                let taskCheckResultHtml = '';
                if (task.checkResult !== undefined && task.checkResult !== null) {
                    if (task.checkResult === true) {
                        taskCheckResultHtml = '<span class="check-result success">Done</span>';
                    } else {
                        taskCheckResultHtml = `<span class="check-result error">校验不通过${task.checkMsg ? ': ' + task.checkMsg : ''}</span>`;
                    }
                }
                
                html += `
                    <div class="section process-task">
                        <div class="section-header task-title">
                            <h4>${task.name}
                `;

                if (task.checkContent) {
                    html += `<span style="color: blue; font-size: smaller;">(${task.checkContent})</span>`;
                }
                html += `
                            ${taskCheckResultHtml}</h4>
                            <button class="toggle-btn">+</button>
                        </div>
                        <div class="section-content task-content" style="display: none;">
                `;

                // 检查是否有数据需要展示
                if (task.data) {
                    html += formatTaskData(task.data, task.checkMode);
                } else {
                    html += '<p class="no-data">暂无数据</p>';
                }
                
                // 处理第三层数据（如果有）
                if (task.children && Array.isArray(task.children) && task.children.length > 0) {
                    task.children.forEach(subTask => {
                        // 添加校验结果标识
                        let subTaskCheckResultHtml = '';
                        if (subTask.checkResult !== undefined && subTask.checkResult !== null) {
                            if (subTask.checkResult === true) {
                                subTaskCheckResultHtml = '<span class="check-result success">Done</span>';
                            } else {
                                subTaskCheckResultHtml = `<span class="check-result error">校验不通过${subTask.checkMsg ? ': ' + subTask.checkMsg : ''}</span>`;
                            }
                        }
                        
                        html += `
                            <div class="section process-subtask">
                                <div class="section-header subtask-title">
                                    <h5>${subTask.name}${subTaskCheckResultHtml}</h5>
                                    <button class="toggle-btn">+</button>
                                </div>
                                <div class="section-content subtask-content" style="display: none;">
                        `;
                        
                        // 检查是否有数据需要展示
                        if (subTask.data) {
                            html += formatTaskData(subTask.data, subTask.checkMode);
                        } else {
                            html += '<p class="no-data">暂无数据</p>';
                        }
                        
                        html += `
                                </div>
                            </div>
                        `;
                    });
                }
                
                html += `
                        </div>
                    </div>
                `;
            });
        } else {
            html += '<p class="no-data">暂无任务</p>';
        }
        
        html += `
                </div>
            </div>
        `;
    });
    
    html += '</div>';
    return html;
}

// 根据不同的checkMode格式化任务数据
function formatTaskData(data, checkMode) {
    let html = '';
    
    // 如果data为null或undefined，直接返回无数据提示
    if (!data) {
        return '<p class="no-data">暂无数据</p>';
    }
    
    switch (checkMode) {
        case 'jiraIssue':
            // jiraIssue类型的data是一个对象，包含Story、Task、Bug等键
            html += formatJiraIssues(data);
            break;
        case 'lastAppRelease':
            // appRelease类型的data是一个数组
            html += formatLastAppRelease(data);
            break;
        case 'lastSqlRelease':
            // sqlRelease类型的data是一个数组
            html += formatLastSqlRelease(data);
            break;
        case 'nacosRelease':
            // nacosRelease类型的data可能是数组或null
            html += formatLastNacosRelease(data);
            break;
        case 'margeCheck':
            // margeCheck类型的data是一个对象，包含各个组件的分支合并情况
            html += formatMargeCheck(data);
            break;
        case 'appRelease':
            // appRelease类型的data是一个数组
            html += formatAppRelease(data);
            break;
        case 'sqlRelease':
            // sqlRelease类型的data是一个数组
            html += formatSqlRelease(data);
            break;
        default:
            // 根据data类型选择合适的格式化方法
            if (Array.isArray(data)) {
                html += formatGenericArray(data);
            } else if (typeof data === 'object') {
                // 检查是否包含Story、Task、Bug等键，如果有则按jiraIssue格式处理
                if (data.Story || data.Task || data.Bug || data['Sub-task']) {
                    html += formatJiraIssues(data);
                } else {
                    // 其他对象类型，转为通用表格
                    html += formatGenericObject(data);
                }
            } else {
                html += `<p class="data-value">${data}</p>`;
            }
    }
    
    return html;
}

// 格式化Jira Issue数据
function formatJiraIssues(data) {
    try {
        data = JSON.parse(data);
    } catch (e) {
        console.error('解析Jira Issue数据失败:', e);
        return '<p class="error">解析Jira Issue数据失败</p>';
    }
    
    if (!data) return '<p class="no-data">暂无Issue数据</p>';
    
    let html = '';
    const issueTypes = ['Story', 'Task', 'Bug', 'Sub-task'];
    
    issueTypes.forEach(type => {
        if (data[type] && data[type].length > 0) {
            html += `
                <div class="section" data-type="jira-issue">
                    <div class="section-header">
                        <h4>${type} (${data[type].length})</h4>
                        <button class="toggle-btn">+</button>
                    </div>
                    <div class="section-content" style="display: none;">
                        <table class="issue-table">
                            <thead>
                                <tr>
                                    <th>Issue</th>
                                    <th>Title</th>
                                    <th>Assignee</th>
                                    <th>Status</th>
                                    <th>Components</th>
                                </tr>
                            </thead>
                            <tbody>
            `;
            
            data[type].forEach(issue => {
                html += `
                    <tr>
                        <td>${issue.key || '-'}</td>
                        <td><a href="https://jira.sgsonline.com.cn/browse/${issue.key}" target="_blank">${issue.summary || '-'}</a></td>
                        <td>${issue.assignee || '-'}</td>
                        <td>${issue.status || '-'}</td>
                        <td>${Array.isArray(issue.components) ? issue.components.join(', ') : '-'}</td>
                    </tr>
                `;
            });
            
            html += `
                            </tbody>
                        </table>
                    </div>
                </div>
            `;
        }
    });
    
    return html || '<p class="no-data">暂无Issue数据</p>';
}

// 格式化应用发布数据
function formatLastAppRelease(data) {
    try {
        if (typeof data === 'string') {
            data = JSON.parse(data);
        }
    } catch (e) {
        console.error('解析应用发布数据失败:', e);
        return '<p class="error">解析应用发布数据失败</p>';
    }
    
    if (!data || !Array.isArray(data) || data.length === 0) {
        return '<p class="no-data">暂无应用发布数据</p>';
    }
    
    let html = `
        <div class="section" data-type="app-release">
            <div class="section-header">
                <h4>应用发布 (${data.length})</h4>
                <button class="toggle-btn">+</button>
            </div>
            <div class="section-content" style="display: none;">
                <table class="release-table">
                    <thead>
                        <tr>
                            <th>应用名称</th>
                            <th>环境</th>
                            <th>备注</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody>
    `;
    
    data.forEach(item => {
        html += `
            <tr>
                <td>${item.appName || '-'}</td>
                <td>${item.envName || '-'}</td>
                <td>${item.remark || '-'}</td>
                <td><span class="status-badge ${getStatusClass(item.status)}">${item.status || '待发布'}</span></td>
            </tr>
        `;
    });
    
    html += `
                    </tbody>
                </table>
            </div>
        </div>
    `;
    
    return html;
}


// 格式化应用发布数据
function formatAppRelease(data) {
    try {
        if (typeof data === 'string') {
            data = JSON.parse(data);
        }
    } catch (e) {
        console.error('解析应用发布数据失败:', e);
        return '<p class="error">解析应用发布数据失败</p>';
    }

    if (!data || !Array.isArray(data) || data.length === 0) {
        return '<p class="no-data">暂无应用发布数据</p>';
    }

    let html = `
        <div class="section" data-type="app-release">
            <div class="section-header">
                <h4>应用发布 (${data.length})</h4>
                <button class="toggle-btn">+</button>
            </div>
            <div class="section-content" style="display: none;">
                <table class="release-table">
                    <thead>
                        <tr>
                            <th>应用名称</th>
                            <th>工单号</th>
                            <th>环境</th>
                            <th>备注</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody>
    `;

    data.forEach(item => {
        html += `
            <tr>
                <td>${item.appName || '-'}</td>
                <td>${item.orderNo || '-'}</td>
                <td>${item.envName || '-'}</td>
                <td>${item.remark || '-'}</td>
                <td><span class="status-badge ${getStatusClass(item.status)}">${item.status || '待发布'}</span></td>
            </tr>
        `;
    });

    html += `
                    </tbody>
                </table>
            </div>
        </div>
    `;

    return html;
}

// 格式化SQL发布数据
function formatLastSqlRelease(data) {
    try {
        if (typeof data === 'string') {
            data = JSON.parse(data);
        }
    } catch (e) {
        console.error('解析SQL发布数据失败:', e);
        return '<p class="error">解析SQL发布数据失败</p>';
    }
    
    if (!data || !Array.isArray(data) || data.length === 0) {
        return '<p class="no-data">暂无SQL发布数据</p>';
    }
    
    let html = `
        <div class="section" data-type="sql-release">
            <div class="section-header">
                <h4>数据库发布 (${data.length})</h4>
                <button class="toggle-btn">+</button>
            </div>
            <div class="section-content" style="display: none;">
                <table class="release-table">
                    <thead>
                        <tr>
                            <th>数据库名称</th>
                            <th>环境</th>
                            <th>备注</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
    `;
    
    data.forEach((item, index) => {
        html += `
            <tr>
                <td>${item.dbName || '-'}</td>
                <td>${item.envName || '-'}</td>
                <td>${item.remark || '-'}</td>
                <td><span class="status-badge ${getStatusClass(item.status)}">${item.status || '待发布'}</span></td>
                <td><button class="detail-btn" data-index="${index}" data-db-name="${item.dbName || '未知数据库'}" data-issue="${item.issue || '无关联Issue'}" data-sql="${encodeURIComponent(item.dbSql || '无SQL内容')}">详情</button></td>
            </tr>
        `;
    });
    
    html += `
                    </tbody>
                </table>
            </div>
        </div>
    `;
    
    return html;
}

// 格式化SQL发布数据
function formatSqlRelease(data) {
    try {
        if (typeof data === 'string') {
            data = JSON.parse(data);
        }
    } catch (e) {
        console.error('解析SQL发布数据失败:', e);
        return '<p class="error">解析SQL发布数据失败</p>';
    }

    if (!data || !Array.isArray(data) || data.length === 0) {
        return '<p class="no-data">暂无SQL发布数据</p>';
    }

    let html = `
        <div class="section" data-type="sql-release">
            <div class="section-header">
                <h4>数据库发布 (${data.length})</h4>
                <button class="toggle-btn">+</button>
            </div>
            <div class="section-content" style="display: none;">
                <table class="release-table">
                    <thead>
                        <tr>
                            <th>数据库名称</th>
                            <th>工单号</th>
                            <th>环境</th>
                            <th>备注</th>
                            <th>关联工单号</th>
                            <th>工单检查</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
    `;

    data.forEach((item, index) => {
        html += `
            <tr>
                <td>${item.dbName || '-'}</td>
                <td>${item.orderNo || '-'}</td>
                <td>${item.envName || '-'}</td>
                <td>${item.remark || '-'}</td>
                <td>${item.relateOrderNo || '-'}</td>
                <td>${item.margeContent || '-'}</td>
                <td><span class="status-badge ${getStatusClass(item.status)}">${item.status || '待发布'}</span></td>
                <td><button class="detail-btn" data-index="${index}" data-db-name="${item.dbName || '未知数据库'}" data-issue="${item.issue || '无关联Issue'}" data-sql="${encodeURIComponent(item.dbSql || '无SQL内容')}">详情</button></td>
            </tr>
        `;
    });

    html += `
                    </tbody>
                </table>
            </div>
        </div>
    `;

    return html;
}

// 格式化Nacos发布数据
function formatLastNacosRelease(data) {
    try {
        if (typeof data === 'string') {
            data = JSON.parse(data);
        }
    } catch (e) {
        console.error('解析Nacos发布数据失败:', e);
        return '<p class="error">解析Nacos发布数据失败</p>';
    }
    
    if (!data || !Array.isArray(data) || data.length === 0) {
        return '<p class="no-data">暂无Nacos配置发布数据</p>';
    }
    
    let html = `
        <div class="section" data-type="nacos-release">
            <div class="section-header">
                <h4>配置发布 (${data.length})</h4>
                <button class="toggle-btn">+</button>
            </div>
            <div class="section-content" style="display: none;">
                <table class="release-table">
                    <thead>
                        <tr>
                            <th>配置名称</th>
                            <th>环境</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody>
    `;
    
    data.forEach(item => {
        html += `
            <tr>
                <td>${item.configName || '-'}</td>
                <td>${item.envName || '-'}</td>
                <td><span class="status-badge ${getStatusClass(item.status)}">${item.status || '待发布'}</span></td>
            </tr>
        `;
    });
    
    html += `
                    </tbody>
                </table>
            </div>
        </div>
    `;
    
    return html;
}

// 格式化通用数组数据
function formatGenericArray(data) {
    if (!Array.isArray(data) || data.length === 0) {
        return '<p class="no-data">暂无数据</p>';
    }
    
    // 获取所有可能的键
    const allKeys = new Set();
    data.forEach(item => {
        if (typeof item === 'object' && item !== null) {
            Object.keys(item).forEach(key => allKeys.add(key));
        }
    });
    
    // 如果没有找到键，可能是简单数组
    if (allKeys.size === 0) {
        let html = '<ul class="generic-list">';
        data.forEach(item => {
            html += `<li>${item}</li>`;
        });
        html += '</ul>';
        return html;
    }
    
    // 转换为数组并排序
    const keys = Array.from(allKeys).sort();
    
    let html = `
        <table class="generic-table">
            <thead>
                <tr>
                    ${keys.map(key => `<th>${key}</th>`).join('')}
                </tr>
            </thead>
            <tbody>
    `;
    
    data.forEach(item => {
        html += '<tr>';
        keys.forEach(key => {
            const value = item[key] !== undefined ? item[key] : '-';
            html += `<td>${value}</td>`;
        });
        html += '</tr>';
    });
    
    html += `
            </tbody>
        </table>
    `;
    
    return html;
}

// 新增：格式化通用对象数据
function formatGenericObject(data) {
    if (!data || typeof data !== 'object' || Object.keys(data).length === 0) {
        return '<p class="no-data">暂无数据</p>';
    }
    
    let html = `
        <table class="generic-table">
            <thead>
                <tr>
                    <th>属性</th>
                    <th>值</th>
                </tr>
            </thead>
            <tbody>
    `;
    
    // 遍历对象的所有键值对
    Object.entries(data).forEach(([key, value]) => {
        html += '<tr>';
        html += `<td>${key}</td>`;
        
        // 根据值的类型进行格式化
        if (value === null || value === undefined) {
            html += '<td>-</td>';
        } else if (typeof value === 'object') {
            if (Array.isArray(value)) {
                html += `<td>${JSON.stringify(value)}</td>`;
            } else {
                html += `<td>${JSON.stringify(value)}</td>`;
            }
        } else {
            html += `<td>${value}</td>`;
        }
        
        html += '</tr>';
    });
    
    html += `
            </tbody>
        </table>
    `;
    
    return html;
}

// 切换section的显示/隐藏
function toggleSection(button) {
    console.log('Toggle button clicked:', button);
    
    // 查找最近的section或section-header
    const section = button.closest('.section') || button.closest('.section-header');
    if (!section) {
        console.error('找不到section元素');
        return;
    }
    
    // 查找content元素
    let content;
    if (button.closest('.section-header')) {
        // 如果按钮在section-header内，content是下一个兄弟元素
        content = button.closest('.section-header').nextElementSibling;
    } else {
        // 否则在section内查找section-content
        content = section.querySelector('.section-content');
    }
    
    if (!content) {
        console.error('找不到content元素');
        return;
    }
    
    console.log('Found content element:', content);
    
    // 切换显示状态
    if (content.style.display === 'none' || content.style.display === '') {
        // 展开
        content.style.display = 'block';
        button.textContent = '-';
    } else {
        // 收起
        content.style.display = 'none';
        button.textContent = '+';
    }
}

// 显示SQL详情
function showSqlDetail(index, dbName, issue, sqlContent) {
    // 创建模态框
    const modal = document.createElement('div');
    modal.className = 'sql-modal';
    
    // 创建模态框内容
    modal.innerHTML = `
        <div class="sql-modal-content">
            <div class="sql-modal-header">
                <h3>SQL详情</h3>
                <span class="sql-modal-close">&times;</span>
            </div>
            <div class="sql-modal-body">
                <div class="sql-item">
                    <div class="sql-header">
                        <span class="sql-db">${dbName}</span>
                        <span class="sql-issue">${issue}</span>
                    </div>
                    <pre class="sql-content">${decodeURIComponent(sqlContent)}</pre>
                </div>
            </div>
        </div>
    `;
    
    // 添加到body
    document.body.appendChild(modal);
    
    // 添加关闭按钮事件
    const closeBtn = modal.querySelector('.sql-modal-close');
    closeBtn.addEventListener('click', function() {
        closeSqlModal(modal);
    });
    
    // 显示模态框
    setTimeout(() => {
        modal.style.opacity = '1';
    }, 10);
}

// 关闭SQL详情模态框
function closeSqlModal(modal) {
    modal.style.opacity = '0';
    setTimeout(() => {
        if (modal.parentNode) {
            modal.parentNode.removeChild(modal);
        }
    }, 300);
}

// 根据状态获取对应的CSS类
function getStatusClass(status) {
    if (!status) return 'status-pending';
    
    switch(status.toLowerCase()) {
        case 'success':
        case '成功':
        case '已发布':
            return 'status-success';
        case 'fail':
        case 'failed':
        case '失败':
            return 'status-error';
        case 'pending':
        case '待发布':
            return 'status-pending';
        case 'processing':
        case '发布中':
            return 'status-warning';
        default:
            return 'status-pending';
    }
}

// 格式化分支合并检查数据
function formatMargeCheck(data) {
    // 确保data是对象
    if (typeof data === 'string') {
        try {
            data = JSON.parse(data);
        } catch (e) {
            console.error('解析margeCheck数据失败:', e);
            return '<p class="error">解析分支合并数据失败</p>';
        }
    }
    
    if (!data || typeof data !== 'object' || Object.keys(data).length === 0) {
        return '<p class="no-data">暂无分支合并数据</p>';
    }
    
    let html = `
        <div class="section" data-type="marge-check">
            <div class="section-header">
                <h4>分支合并检查</h4>
                <button class="toggle-btn">+</button>
            </div>
            <div class="section-content" style="display: none;">
                <table class="marge-table">
                    <thead>
                        <tr>
                            <th>组件</th>
                            <th>分支名称</th>
                            <th>合并状态</th>
                        </tr>
                    </thead>
                    <tbody>
    `;
    
    // 遍历所有组件
    let hasData = false;
    for (const [component, branches] of Object.entries(data)) {
        if (Array.isArray(branches) && branches.length > 0) {
            hasData = true;
            branches.forEach(branch => {
                const branchName = branch.branchName || '-';
                const isMerged = branch.isMarge === 1;
                const statusClass = isMerged ? 'status-success' : 'status-error';
                const statusText = isMerged ? '已合并' : '未合并';
                
                html += `
                    <tr>
                        <td>${component}</td>
                        <td>${branchName}</td>
                        <td><span class="status-badge ${statusClass}">${statusText}</span></td>
                    </tr>
                `;
            });
        } else if (Array.isArray(branches) && branches.length === 0) {
            // 显示空分支的组件
            html += `
                <tr>
                    <td>${component}</td>
                    <td colspan="2"><span class="color: red; no-data">无分支</span></td>
                </tr>
            `;
        }
    }
    
    if (!hasData) {
        html += `
            <tr>
                <td colspan="3"><p class="no-data">暂无分支合并数据</p></td>
            </tr>
        `;
    }
    
    html += `
                    </tbody>
                </table>
            </div>
        </div>
    `;
    
    return html;
}

// 初始化事件监听
document.addEventListener('DOMContentLoaded', () => {
    loadReleaseVersions();

    const searchInput = document.getElementById('releaseQueryInput');
    const optionsContainer = document.getElementById('releaseQueryOptions');
    const resultDiv = document.getElementById('releaseQueryResult');

    searchInput.addEventListener('input', (e) => {
        const searchText = e.target.value;
        filterVersions(searchText);
        optionsContainer.style.display = 'block';
    });

    searchInput.addEventListener('focus', () => {
        optionsContainer.style.display = 'block';
    });

    document.addEventListener('click', (e) => {
        if (!e.target.closest('.select-container')) {
            optionsContainer.style.display = 'none';
        }
    });

    // 为结果区域添加事件委托，处理所有toggle-btn点击
    resultDiv.addEventListener('click', (e) => {
        if (e.target.classList.contains('toggle-btn')) {
            console.log('Toggle button clicked in event delegation');
            toggleSection(e.target);
        }
        
        // 处理SQL详情按钮点击
        if (e.target.classList.contains('detail-btn')) {
            const index = e.target.getAttribute('data-index');
            const dbName = e.target.getAttribute('data-db-name');
            const issue = e.target.getAttribute('data-issue');
            const sqlContent = e.target.getAttribute('data-sql');
            showSqlDetail(index, dbName, issue, sqlContent);
        }
    });

    document.getElementById('releaseQueryForm').addEventListener('submit', async (event) => {
        event.preventDefault();
        
        // 获取所有选中的版本
        const selectedTags = document.querySelectorAll('#selectedReleases .release-tag');
        const versions = Array.from(selectedTags).map(tag => tag.dataset.version);
        
        const envUat = document.getElementById('envUat');
        const envProd = document.getElementById('envProd');
        
        // 检查是否选择了环境
        if (!envUat || !envProd || (!envUat.checked && !envProd.checked)) {
            resultDiv.innerHTML = '<div class="error">请选择环境（UAT或prod）</div>';
            return;
        }
        
        // 获取选中的环境
        const env = envUat.checked ? 'UAT' : 'prod';

        if (versions.length === 0) {
            resultDiv.innerHTML = '<div class="error">请至少选择一个版本号</div>';
            return;
        }

        try {
            resultDiv.innerHTML = '<div class="loading">查询中...</div>';
            
            // 获取Release流程信息，传入环境参数和版本数组
            const processData = await queryReleaseProcess(versions, env);
            let html = formatProcessData(processData);
            
            resultDiv.innerHTML = html;
            
            // 添加调试信息
            console.log('结果已渲染，查找toggle-btn元素');
            const toggleBtns = resultDiv.querySelectorAll('.toggle-btn');
            console.log(`找到 ${toggleBtns.length} 个toggle-btn元素`);
        } catch (error) {
            resultDiv.innerHTML = `<div class="error">查询失败: ${error.message}</div>`;
        }
    });

    // 添加CSS样式
    const style = document.createElement('style');
    style.textContent = `
        /* 整体样式 */
        .release-process-container {
            font-family: Arial, sans-serif;
            color: #333;
            margin: 20px 0;
        }
        
        /* 阶段样式 */
        .process-phase {
            margin-bottom: 30px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        
        .phase-title {
            background-color: #f5f5f5;
            padding: 12px 15px;
            margin: 0;
            font-size: 18px;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
//            justify-content: space-between;
            align-items: center;
        }
        
        .phase-content {
            padding: 15px;
        }
        
        /* 任务样式 */
        .process-task {
            margin-bottom: 20px;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
            overflow: hidden;
        }
        
        .task-title {
            background-color: #f8f8f8;
            padding: 10px 15px;
            margin: 0;
            font-size: 16px;
            border-bottom: 1px solid #e8e8e8;
            display: flex;
//            justify-content: space-between;
            align-items: center;
        }
        
        .task-content {
            padding: 15px;
        }
        
        /* 子任务样式 */
        .process-subtask {
            margin-top: 15px;
            border: 1px solid #eee;
            border-radius: 4px;
        }
        
        .subtask-title {
            background-color: #fafafa;
            padding: 8px 12px;
            margin: 0;
            font-size: 14px;
            border-bottom: 1px solid #eee;
            display: flex;
//            justify-content: space-between;
            align-items: center;
        }
        
        .subtask-content {
            padding: 12px;
        }
        
        /* 校验结果样式 */
        .check-result {
            font-size: 13px;
            padding: 3px 8px;
            border-radius: 4px;
            margin-left: 10px;
            white-space: nowrap;
        }
        
        .check-result.success {
            background-color: #e8f5e9;
            color: #2e7d32;
        }
        
        .check-result.error {
            background-color: #ffebee;
            color: #c62828;
        }
        
        /* Section样式 */
        .section {
            margin-bottom: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: #f5f5f5;
            padding: 8px 12px;
            cursor: pointer;
        }
        
        .section-header h4 {
            margin: 0;
            font-size: 15px;
        }
        
        .toggle-btn {
            background: none;
            border: none;
            font-size: 18px;
            font-weight: bold;
            color: #555;
            cursor: pointer;
            width: 24px;
            height: 24px;
            line-height: 24px;
            text-align: center;
        }
        
        .section-content {
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }
        
        /* 表格样式 */
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        
        th, td {
            padding: 8px 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        th {
            background-color: #f8f8f8;
            font-weight: bold;
        }
        
        tr:hover {
            background-color: #f9f9f9;
        }
        
        /* 状态标签 */
        .status-badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status-success {
            background-color: #e6f7e6;
            color: #2e7d32;
        }
        
        .status-error {
            background-color: #fdecea;
            color: #d32f2f;
        }
        
        .status-warning {
            background-color: #fff8e1;
            color: #ff8f00;
        }
        
        .status-pending {
            background-color: #e3f2fd;
            color: #1976d2;
        }
        
        /* 详情按钮 */
        .detail-btn {
//            background-color: #f0f0f0;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 4px 8px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .detail-btn:hover {
            background-color: #e0e0e0;
        }
        
        /* SQL模态框 */
        .sql-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .sql-modal-content {
            background-color: white;
            border-radius: 8px;
            width: 80%;
            max-width: 800px;
            max-height: 80vh;
            overflow: hidden;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        
        .sql-modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            background-color: #f5f5f5;
            border-bottom: 1px solid #e0e0e0;
        }
        
        .sql-modal-header h3 {
            margin: 0;
            font-size: 18px;
        }
        
        .sql-modal-close {
            font-size: 24px;
            font-weight: bold;
            cursor: pointer;
        }
        
        .sql-modal-body {
            padding: 20px;
            overflow-y: auto;
            max-height: calc(80vh - 60px);
        }
        
        .sql-item {
            margin-bottom: 15px;
        }
        
        .sql-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }
        
        .sql-db {
            font-weight: bold;
            color: #333;
        }
        
        .sql-issue {
            color: #666;
        }
        
        .sql-content {
            background-color: #f8f8f8;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            padding: 12px;
            overflow-x: auto;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 13px;
            line-height: 1.5;
        }
        
        /* 无数据提示 */
        .no-data {
            color: #888;
            font-style: italic;
            padding: 10px;
            text-align: center;
        }
        
        /* 错误提示 */
        .error {
            color: #d32f2f;
            padding: 10px;
            background-color: #fdecea;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        /* 加载中提示 */
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        
        /* 表单样式 */
        .form-container {
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .form-group {
            display: flex;
            align-items: center;
            margin-bottom: 0;
        }
        
        .form-label {
            margin-right: 10px;
            white-space: nowrap;
        }
        
        .radio-group {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .radio-label {
            display: flex;
            align-items: center;
            cursor: pointer;
            margin: 0;
        }
        
        .radio-label input[type="radio"] {
            margin-right: 5px;
        }
        
        /* 按钮样式 */
        button[type="submit"] {
            background-color: #4CAF50;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            height: 36px;
        }
        
        button[type="submit"]:hover {
            background-color: #45a049;
        }
        
        /* 分支合并表格样式 */
        .marge-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        
        .marge-table th, 
        .marge-table td {
            padding: 8px 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        .marge-table th {
            background-color: #f8f8f8;
            font-weight: bold;
        }
        
        .marge-table tr:hover {
            background-color: #f9f9f9;
        }
        
        /* 节点和内容样式 */
        .section {
            margin-bottom: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 15px;
            background-color: #f5f5f5;
            border-bottom: 1px solid #e0e0e0;
            cursor: pointer;
            position: relative; /* 添加相对定位 */
        }
        
        .section-header h4, 
        .section-header h5 {
            margin: 0;
            font-size: 16px;
            padding-right: 30px; /* 为按钮预留空间 */
            word-break: break-word; /* 允许长文本换行 */
            flex: 1; /* 让标题占据剩余空间 */
        }
        
        .section-content {
            padding: 15px;
            background-color: #fff;
        }
        
        .toggle-btn {
            position: absolute; /* 使用绝对定位 */
            right: 15px; /* 固定在右侧 */
            top: 50%; /* 垂直居中 */
            transform: translateY(-50%); /* 精确垂直居中 */
            background-color: #e0e0e0;
            border: none;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-weight: bold;
            font-size: 16px;
            z-index: 10; /* 确保按钮在最上层 */
        }
        
        .toggle-btn:hover {
            background-color: #d0d0d0;
        }
        
        /* 流程阶段样式 */
        .process-phase {
            margin-bottom: 20px;
            border: 1px solid #d0d0d0;
            border-radius: 4px;
        }
        
        .phase-title {
            background-color: #e8f5e9;
        }
        
        .phase-content {
            padding: 15px;
        }
        
        .process-task {
            margin-bottom: 15px;
        }
        
        .task-title {
            background-color: #e3f2fd;
        }
        
        .process-subtask {
            margin-left: 20px;
            margin-bottom: 10px;
        }
        
        .subtask-title {
            background-color: #f3e5f5;
        }
        
        /* 状态标签 */
        .check-result {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            margin-left: 10px;
            word-break: break-word; /* 允许长文本换行 */
            max-width: 100%; /* 限制最大宽度 */
        }
        
        .check-result.success {
            background-color: #e6f7e6;
            color: #2e7d32;
        }
        
        .check-result.error {
            background-color: #fdecea;
            color: #d32f2f;
        }
        .task-check-result {
            padding: 0 15px 8px;
            background-color: #e3f2fd;
            border-bottom: 1px solid #d0d0d0;
        }
        .selected-versions-container {
            flex-basis: 100%;
            margin-top: 5px;
            margin-bottom: 10px;
        }
        
        .selected-releases {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            min-height: 30px;
            max-width: 600px;
            padding: 5px;
            border: 1px dashed #ddd;
            border-radius: 4px;
            background-color: #fafafa;
        }
        
        .release-tag {
            display: flex;
            align-items: center;
            background-color: #e0f2f1;
            border: 1px solid #b2dfdb;
            border-radius: 16px;
            padding: 4px 10px;
            font-size: 12px;
            color: #00796b;
            box-shadow: 0 1px 2px rgba(0,0,0,0.05);
        }
        
        .release-tag span {
            max-width: 150px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .remove-tag {
            background: none;
            border: none;
            color: #00796b;
            cursor: pointer;
            font-size: 14px;
            padding: 0 0 0 5px;
            margin: 0;
            line-height: 1;
        }
        
        .remove-tag:hover {
            color: #f44336;
            background: none;
        }
        
        .form-container {
            flex-wrap: wrap;
        }
    `;
    document.head.appendChild(style);
});
