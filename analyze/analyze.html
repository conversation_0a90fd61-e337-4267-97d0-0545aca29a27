<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>日志分析</title>
    <style>
        body {
            margin: 0;
            padding: 15px;
            font-family: Arial, sans-serif;
            font-size: 14px;
        }

        h2 {
            margin: 0 0 15px 0;
            font-size: 16px;
            color: #333;
        }

        .form-container {
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #666;
        }

        input, select {
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        input:focus, select:focus {
            border-color: #1e88e5;
            outline: none;
        }

        button {
            padding: 8px 20px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        button:hover {
            background-color: #45a049;
        }

        #result {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }

        .error {
            color: #f44336;
            margin-top: 5px;
            font-size: 12px;
        }

        .loading {
            color: #666;
            text-align: center;
            padding: 20px;
        }

        pre {
            margin: 0;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
    </style>
</head>
<body>
    <h2>日志分析</h2>
    <div class="form-container">
        <form id="analyzeForm">
            <div class="form-group">
                <label for="traceId">TraceId:</label>
                <input type="text" id="traceId" required placeholder="请输入TraceId">
            </div>
            <div class="form-group">
                <label for="appName">应用名称:</label>
                <select id="appName" required>
                    <option value="">请选择应用</option>
                </select>
            </div>
            <button type="submit">分析</button>
        </form>
    </div>
    <div id="result"></div>
    <script src="analyze.js"></script>
</body>
</html>