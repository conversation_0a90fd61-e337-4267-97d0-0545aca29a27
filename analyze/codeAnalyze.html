<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>代码分析</title>
    <style>
        body {
            margin: 0;
            padding: 15px;
            font-family: Arial, sans-serif;
            font-size: 14px;
        }

        h2 {
            margin: 0 0 15px 0;
            font-size: 16px;
            color: #333;
        }

        h3 {
            margin: 15px 0 10px 0;
            font-size: 15px;
            color: #444;
        }

        .graph-title {
            margin-top: 20px;
            margin-bottom: 10px;
        }

        .ai-analysis-section {
            margin-top: 20px;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 5px;
            border: 1px solid #eee;
        }

        .coming-soon {
            color: #888;
            font-style: italic;
            text-align: center;
            margin: 10px 0;
        }

        .form-container {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .form-row {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            align-items: center; /* 改为居中对齐 */
        }
        
        .form-group {
            margin-bottom: 0;
            display: flex;
            align-items: center; /* 确保所有元素居中对齐 */
            height: 36px; /* 固定高度确保一致性 */
        }
        
        /* 标签样式统一 */
        label {
            display: inline-block;
            width: 80px;
            margin-right: 10px;
            margin-bottom: 0;
            font-weight: bold;
            color: #666;
            text-align: right;
            line-height: 36px; /* 与容器高度一致 */
        }
        
        .form-group-project {
            width: 350px; /* 调整宽度 */
            flex-shrink: 0;
        }
        
        .form-group-type {
            flex-grow: 1;
            display: flex;
            align-items: center;
            min-width: 400px; /* 确保有足够的宽度 */
        }
        
        .radio-group {
            display: flex;
            gap: 30px; /* 增加间距到30px */
            align-items: center;
            flex-wrap: nowrap;
            white-space: nowrap;
            height: 36px;
        }
        
        .radio-label {
            display: flex;
            align-items: center;
            cursor: pointer;
            font-weight: normal;
            white-space: nowrap;
            height: 36px;
            padding: 0 5px; /* 添加内边距 */
        }
        
        .radio-label input[type="radio"] {
            margin-right: 10px; /* 增加右侧间距 */
            position: relative;
            top: 0;
        }
        
        .radio-label input {
            margin-right: 5px;
            width: auto;
        }
        
        .form-group-interface, .form-group-class, .form-group-method {
            flex-grow: 1;
            min-width: 250px;
        }
        
        /* 可搜索下拉框容器 */
        .searchable-select-container {
            flex: 1;
            height: 36px; /* 固定高度 */
            display: flex;
            align-items: center;
        }
        
        /* 可搜索下拉框样式 */
        .searchable-select {
            position: relative;
            width: 100%;
            height: 100%;
        }
        
        .searchable-select input {
            width: 100%;
            height: 36px; /* 固定高度 */
            padding: 0 8px; /* 调整内边距 */
            box-sizing: border-box;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .searchable-select input:focus {
            border-color: #1e88e5;
            outline: none;
        }
        
        .searchable-select .options {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            max-height: 200px;
            overflow-y: auto;
            background: white;
            border: 1px solid #ddd;
            border-radius: 0 0 4px 4px;
            z-index: 10;
            display: none;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .searchable-select .option {
            padding: 8px 12px;
            cursor: pointer;
        }
        
        .searchable-select .option:hover {
            background-color: #f5f5f5;
        }
        
        .searchable-select .option.selected {
            background-color: #e3f2fd;
        }
        
        /* 按钮样式优化 */
        .form-group-button {
            display: flex;
            align-items: center; /* 居中对齐 */
            height: 36px; /* 固定高度 */
        }
        
        button[type="submit"] {
            padding: 0 20px; /* 调整内边距 */
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            height: 36px; /* 与输入框高度一致 */
            line-height: 36px; /* 文本垂直居中 */
        }
        
        button[type="submit"]:hover {
            background-color: #45a049;
        }

        #result {
            margin-top: 20px;
            min-height: 400px;
        }

        .error {
            color: #f44336;
            margin-top: 5px;
            font-size: 12px;
        }

        .loading {
            color: #666;
            text-align: center;
            padding: 20px;
        }

        /* 图形展示相关样式 */
        #graph-container {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            position: relative;
            overflow: auto;
            background-color: #fafafa;
            padding: 20px;
        }

        .node {
            position: absolute;
            width: 250px;
            min-height: 80px;
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 12px;
            box-shadow: 0 3px 8px rgba(0,0,0,0.1);
            cursor: move;
            z-index: 2;
            transition: box-shadow 0.2s ease;
        }

        .node:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,0.15);
        }

        .node.selected {
            border: 2px solid #1e88e5;
            box-shadow: 0 5px 15px rgba(30,136,229,0.3);
        }

        .node-header {
            font-weight: bold;
            margin-bottom: 8px;
            padding-bottom: 8px;
            border-bottom: 1px solid #eee;
            color: #333;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .node-content {
            font-size: 12px;
            color: #666;
            line-height: 1.4;
        }

        .node-content div {
            margin-bottom: 4px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        /* 参数项样式 */
        .param-item {
            display: block;
            margin-bottom: 2px;
            padding-left: 8px;
            color: #555;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-size: 11px;
        }
        
        /* 详情面板中的参数列表样式 */
        .node-detail-content ul {
            margin: 5px 0 15px 0;
            padding-left: 20px;
        }
        
        .node-detail-content li {
            margin-bottom: 5px;
        }
        
        .node-detail-content p {
            margin: 8px 0;
        }
        
        .node-detail-content strong {
            color: #333;
        }

        .connection-line {
            z-index: 1;
        }
        
        /* 序号标签样式 */
        .connection-line text {
            font-family: Arial, sans-serif;
            user-select: none;
            pointer-events: none;
        }
        
        .connection-line circle {
            filter: drop-shadow(0 1px 2px rgba(0,0,0,0.3));
        }

        .node-detail {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 80%;
            max-width: 800px;
            max-height: 80vh;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 5px 25px rgba(0,0,0,0.25);
            padding: 20px;
            z-index: 1000;
            overflow-y: auto;
        }

        .node-detail-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .node-detail-close {
            cursor: pointer;
            font-size: 24px;
            color: #999;
        }

        .node-detail-close:hover {
            color: #333;
        }

        .node-detail-content {
            margin-bottom: 15px;
        }

        .node-detail-code {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 13px;
            line-height: 1.5;
        }

        .overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0,0,0,0.5);
            z-index: 999;
        }
    </style>
</head>
<body>
    <h2>代码分析</h2>
    <form id="analyzeForm">
        <div class="form-container">
            <!-- 第一行：工程选择和查询类型 -->
            <div class="form-row">
                <div class="form-group form-group-project">
                    <label for="projectInput">工程选择:</label>
                    <div class="searchable-select-container">
                        <div class="searchable-select" id="projectSelectContainer">
                            <input type="text" id="projectInput" placeholder="请选择工程" autocomplete="off">
                            <input type="hidden" id="projectName" name="projectName">
                            <div class="options" id="projectOptions"></div>
                        </div>
                    </div>
                </div>
                
                <div class="form-group form-group-type">
                    <label>查询类型:</label>
                    <div class="radio-group">
                        <label class="radio-label">
                            <input type="radio" name="queryType" id="queryTypeInterface" value="interface">
                            按接口地址查询
                        </label>
                        <label class="radio-label">
                            <input type="radio" name="queryType" id="queryTypeMethod" value="method">
                            按方法类查询
                        </label>
                    </div>
                </div>
            </div>
            
            <!-- 第二行：接口地址选择（默认隐藏） -->
            <div class="form-row" id="interfaceRow" style="display:none;">
                <div class="form-group form-group-interface">
                    <label for="interfaceInput">接口地址:</label>
                    <div class="searchable-select-container">
                        <div class="searchable-select" id="interfaceSelectContainer">
                            <input type="text" id="interfaceInput" placeholder="请选择或输入接口地址" autocomplete="off" disabled>
                            <input type="hidden" id="interfaceUrl" name="interfaceUrl">
                            <div class="options" id="interfaceOptions"></div>
                        </div>
                    </div>
                </div>
                <div class="form-group form-group-button">
                    <button type="submit">查询</button>
                </div>
            </div>
            
            <!-- 第二行：类名和方法名选择（默认隐藏） -->
            <div class="form-row" id="classMethodRow" style="display:none;">
                <div class="form-group form-group-class">
                    <label for="classInput">类名:</label>
                    <div class="searchable-select-container">
                        <div class="searchable-select" id="classSelectContainer">
                            <input type="text" id="classInput" placeholder="请选择或输入类名" autocomplete="off" disabled>
                            <input type="hidden" id="className" name="className">
                            <div class="options" id="classOptions"></div>
                        </div>
                    </div>
                </div>
                <div class="form-group form-group-method">
                    <label for="methodInput">方法名:</label>
                    <div class="searchable-select-container">
                        <div class="searchable-select" id="methodSelectContainer">
                            <input type="text" id="methodInput" placeholder="请选择或输入方法名" autocomplete="off" disabled>
                            <input type="hidden" id="methodName" name="methodName">
                            <div class="options" id="methodOptions"></div>
                        </div>
                    </div>
                </div>
                <div class="form-group form-group-button">
                    <button type="submit">查询</button>
                </div>
            </div>
        </div>
    </form>
    <div id="result">
        <h3 class="graph-title">应用方法调用图示</h3>
        <div id="graph-container"></div>
        <div class="ai-analysis-section">
            <h3>AI分析逻辑</h3>
            <p class="coming-soon">此功能后续将完善，敬请期待</p>
        </div>
    </div>
    <script src="codeAnalyze.js"></script>
    <!-- 移除外部库引用 -->
    <!-- <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script> -->
</body>
</html>
