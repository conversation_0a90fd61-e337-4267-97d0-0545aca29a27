// 加载应用列表
async function loadAppList() {
    try {
        const response = await fetch('../json/elkIndex.json');
        const data = await response.json();
        
        const appSelect = document.getElementById('appName');
        appSelect.innerHTML = '<option value="">请选择应用</option>';
        
        data.sort((a, b) => a.description.localeCompare(b.description));
        data.forEach(item => {
            const option = document.createElement('option');
            option.value = item.index;
            option.textContent = item.description;
            appSelect.appendChild(option);
        });
    } catch (error) {
        console.error('加载应用列表失败：', error);
    }
}

// 分析日志
async function analyzeLogs(traceId, appName) {
    if (!chrome.runtime?.id) {
        throw new Error('扩展程序未正确初始化');
    }

    return new Promise((resolve, reject) => {
        chrome.runtime.sendMessage({
            type: 'analyzeLogs',
            data: {
                traceId: traceId,
                appName: appName
            }
        }, response => {
            if (chrome.runtime.lastError) {
                reject(new Error(chrome.runtime.lastError.message));
                return;
            }
            if (response.error) {
                reject(new Error(response.error));
                return;
            }
            resolve(response.data);
        });
    });
}

// 表单提交处理
document.getElementById('analyzeForm').addEventListener('submit', async (event) => {
    event.preventDefault();
    
    const resultDiv = document.getElementById('result');
    const traceId = document.getElementById('traceId').value.trim();
    const appName = document.getElementById('appName').value;

    if (!traceId || !appName) {
        resultDiv.innerHTML = '<div class="error">请填写所有必填字段</div>';
        return;
    }

    try {
        resultDiv.innerHTML = '<div class="loading">分析中...</div>';
        const result = await analyzeLogs(traceId, appName);
        resultDiv.innerHTML = `<pre>${JSON.stringify(result, null, 2)}</pre>`;
    } catch (error) {
        resultDiv.innerHTML = `<div class="error">分析失败: ${error.message}</div>`;
    }
});

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', loadAppList);