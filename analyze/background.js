// 监听来自扩展其他部分的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.type === 'analyzeLogs') {
        handleAnalyzeLogs(request.data)
            .then(data => sendResponse({ data }))
            .catch(error => sendResponse({ error: error.message }));
        return true; // 保持消息通道开放
    }
});

// 处理日志分析请求
async function handleAnalyzeLogs(data) {
    const url = 'https://dify.sgsonline.com.cn/v1/workflows/run';
    
    const response = await fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer app-eoeKHcaXcC0CSzKRb0ticgLM',
            'Accept': 'application/json'
        },
        body: JSON.stringify({
            inputs: {
                searchText: data.traceId,
                searchApp: data.appName
            },
            response_mode: "blocking",
            user: "chrome-plugin"
        })
    });

    if (!response.ok) {
        throw new Error(`请求失败: ${response.status}`);
    }

    return await response.json();
}

// 确保 service worker 保持活跃
chrome.runtime.onInstalled.addListener(() => {
    console.log('Background service worker installed.');
});