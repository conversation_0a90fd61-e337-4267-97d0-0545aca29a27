<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI运维助手</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            min-height: 100vh;
            box-sizing: border-box;
        }
        .container {
            max-width: 95%; /* 使用百分比宽度，接近全屏 */
            margin: 0 auto;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
            min-height: calc(100vh - 40px); /* 减去body的padding */
            display: flex;
            flex-direction: column;
            box-sizing: border-box;
        }
        h1 {
            color: #333;
            margin-top: 0;
            border-bottom: 1px solid #eee;
            padding-bottom: 15px;
            font-size: 28px;
        }
        .select-container {
            margin-bottom: 20px;
            position: relative;
            max-width: 300px;
        }
        .select-container label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #555;
            font-size: 16px;
        }
        .custom-select {
            position: relative;
            width: 100%;
        }
        .select-input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
            cursor: pointer;
            background-color: white;
            font-size: 15px;
        }
        .select-options {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background-color: white;
            border: 1px solid #ddd;
            border-top: none;
            border-radius: 0 0 4px 4px;
            max-height: 250px;
            overflow-y: auto;
            z-index: 10;
            display: none;
        }
        .select-search {
            width: 100%;
            padding: 10px;
            border: none;
            border-bottom: 1px solid #eee;
            box-sizing: border-box;
            font-size: 15px;
        }
        .select-option {
            padding: 10px 12px;
            cursor: pointer;
            font-size: 15px;
        }
        .select-option:hover {
            background-color: #f5f5f5;
        }
        .input-container {
            margin-bottom: 25px;
            flex-grow: 0;
        }
        textarea {
            width: 100%;
            min-height: 120px; /* 增加默认高度 */
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            resize: vertical;
            font-family: inherit;
            font-size: 16px;
            box-sizing: border-box;
            line-height: 1.5;
        }
        .button-container {
            text-align: right;
            margin-top: 10px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 12px 25px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #45a049;
        }
        .examples {
            margin: 0 0 25px 0; /* 调整上边距 */
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 4px;
            border-left: 4px solid #4CAF50;
            font-size: 16px;
            flex-grow: 0;
            max-height: 25vh; /* 限制最大高度 */
            overflow-y: auto; /* 添加滚动条 */
        }
        .examples h3 {
            margin-top: 0;
            color: #333;
            font-size: 18px;
            margin-bottom: 12px;
        }
        .examples p {
            margin: 8px 0;
            color: #666;
            line-height: 1.5;
        }
        .result {
            margin-top: 0; /* 移除上边距 */
            padding: 20px;
            background-color: #f0f7ff;
            border-radius: 4px;
            border-left: 4px solid #2196F3;
            white-space: pre-wrap;
            font-size: 16px;
            flex-grow: 1; /* 让结果区域占据剩余空间 */
            overflow-y: auto; /* 添加滚动条 */
            min-height: 30vh; /* 设置最小高度 */
            display: flex;
            flex-direction: column;
        }
        .result h3 {
            margin-top: 0;
            color: #333;
            font-size: 18px;
            margin-bottom: 12px;
        }
        .result-content {
            font-family: monospace;
            line-height: 1.6;
            font-size: 15px;
            overflow-y: auto;
            flex-grow: 1;
        }
        .loading {
            text-align: center;
            padding: 25px;
            color: #666;
            font-size: 16px;
        }
        .error {
            color: #f44336;
            padding: 15px;
            background-color: #ffebee;
            border-radius: 4px;
            margin-top: 15px;
            font-size: 16px;
        }
        
        /* 响应式调整 */
        @media (min-height: 800px) {
            textarea {
                min-height: 150px; /* 在较高的屏幕上增加输入框高度 */
            }
            .examples {
                max-height: 30vh; /* 在较高的屏幕上增加示例区域高度 */
            }
        }
        
        @media (min-height: 1000px) {
            textarea {
                min-height: 180px; /* 在更高的屏幕上进一步增加输入框高度 */
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>AI运维助手</h1>
        
        <div class="select-container">
            <label for="apiSelect">选择API:</label>
            <div class="custom-select">
                <div class="select-input" id="apiSelectInput">请选择API</div>
                <div class="select-options" id="apiSelectOptions">
                    <input type="text" class="select-search" id="apiSearch" placeholder="搜索API...">
                    <div id="apiOptionsList"></div>
                </div>
            </div>
        </div>
        
        <div class="input-container">
            <textarea id="queryInput" placeholder="请输入您的问题或需求..."></textarea>
            <div class="button-container">
                <button id="sendButton">发送</button>
            </div>
        </div>
        
        <div class="examples" id="examples">
            <h3>示例问题</h3>
            <p>• 请选择API查看相关示例</p>
        </div>
        
        <div class="result" id="result" style="display: none;">
            <h3>查询结果</h3>
            <div class="result-content" id="resultContent"></div>
        </div>
    </div>
    
    <script src="aiOps.js"></script>
</body>
</html>
