// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    const queryInput = document.getElementById('queryInput');
    const sendButton = document.getElementById('sendButton');
    const resultDiv = document.getElementById('result');
    const resultContent = document.getElementById('resultContent');
    const apiSelectInput = document.getElementById('apiSelectInput');
    const apiSelectOptions = document.getElementById('apiSelectOptions');
    const apiSearch = document.getElementById('apiSearch');
    const apiOptionsList = document.getElementById('apiOptionsList');
    const examples = document.getElementById('examples');
    
    // 存储API列表数据
    let apiList = [];
    let selectedApi = null;
    
    // 加载API列表
    async function loadApiList() {
        try {
            const response = await fetch('../json/mcpApi.json');
            apiList = await response.json();
            
            // 渲染API选项
            renderApiOptions(apiList);
        } catch (error) {
            console.error('加载API列表失败:', error);
            showError('加载API列表失败: ' + error.message);
        }
    }
    
    // 渲染API选项
    function renderApiOptions(apis) {
        apiOptionsList.innerHTML = '';
        
        if (apis.length === 0) {
            apiOptionsList.innerHTML = '<div class="select-option">无匹配结果</div>';
            return;
        }
        
        apis.forEach(api => {
            const option = document.createElement('div');
            option.className = 'select-option';
            option.textContent = api.name;
            option.dataset.url = api.url;
            
            option.addEventListener('click', () => {
                selectApi(api);
                apiSelectOptions.style.display = 'none';
            });
            
            apiOptionsList.appendChild(option);
        });
    }
    
    // 选择API
    function selectApi(api) {
        selectedApi = api;
        apiSelectInput.textContent = api.name;
        
        // 更新示例问题
        updateExamples(api.promptExample);
    }
    
    // 更新示例问题
    function updateExamples(promptExample) {
        if (!promptExample) {
            examples.innerHTML = '<h3>示例问题</h3><p>• 暂无示例</p>';
            return;
        }
        
        examples.innerHTML = '<h3>示例问题</h3>';
        
        // 将示例文本按行分割并添加到示例区域
        const exampleLines = promptExample.split('\n');
        exampleLines.forEach(line => {
            if (line.trim()) {
                const p = document.createElement('p');
                p.textContent = line;
                examples.appendChild(p);
            }
        });
    }
    
    // 搜索API
    function searchApi(keyword) {
        if (!keyword) {
            renderApiOptions(apiList);
            return;
        }
        
        const filteredApis = apiList.filter(api => 
            api.name.toLowerCase().includes(keyword.toLowerCase()) || 
            api.url.toLowerCase().includes(keyword.toLowerCase())
        );
        
        renderApiOptions(filteredApis);
    }
    
    // 初始化下拉框事件
    function initSelectEvents() {
        // 点击输入框显示选项
        apiSelectInput.addEventListener('click', () => {
            apiSelectOptions.style.display = 'block';
            apiSearch.focus();
        });
        
        // 搜索框输入事件
        apiSearch.addEventListener('input', () => {
            searchApi(apiSearch.value.trim());
        });
        
        // 点击其他区域关闭选项
        document.addEventListener('click', (e) => {
            if (!apiSelectInput.contains(e.target) && !apiSelectOptions.contains(e.target)) {
                apiSelectOptions.style.display = 'none';
            }
        });
    }
    
    // 发送按钮点击事件
    sendButton.addEventListener('click', async () => {
        const message = queryInput.value.trim();
        
        if (!message) {
            showError('请输入问题内容');
            return;
        }
        
        if (!selectedApi) {
            showError('请先选择API');
            return;
        }
        
        try {
            // 显示加载状态
            resultDiv.style.display = 'block';
            resultContent.innerHTML = '<div class="loading">正在查询中，请稍候...</div>';
            
            // 调用API获取结果
            const result = await queryAIService(message, selectedApi.url);
            
            // 显示结果
            resultContent.innerHTML = result;
        } catch (error) {
            showError(error.message);
        }
    });
    
    // 按Enter键发送
    queryInput.addEventListener('keydown', (e) => {
        // 检查是否按下Ctrl+Enter
        if (e.key === 'Enter' && e.ctrlKey) {
            sendButton.click();
            e.preventDefault();
        }
    });
    
    // 显示错误信息
    function showError(message) {
        resultDiv.style.display = 'block';
        resultContent.innerHTML = `<div class="error">${message}</div>`;
    }
    
    // 调用AI运维服务
    async function queryAIService(message, apiUrl) {
        try {
            // 构建URL，确保正确编码查询参数
            const url = `http://10.169.128.35:7101${apiUrl}?message=${encodeURIComponent(message)}`;
            
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Accept': 'text/plain'
                }
            });
            
            if (!response.ok) {
                throw new Error(`请求失败: ${response.status} ${response.statusText}`);
            }
            
            // 获取文本响应
            return await response.text();
        } catch (error) {
            console.error('AI运维查询失败:', error);
            throw new Error('查询失败: ' + error.message);
        }
    }
    
    // 初始化
    loadApiList();
    initSelectEvents();
});